"use strict";
cc._RF.push(module, 'a1b2cPU5fZ4kKvN7xI0VniQ', 'NoticeRoundStartTest');
// scripts/test/NoticeRoundStartTest.ts

"use strict";
// 测试NoticeRoundStart消息处理的脚本
// 这个脚本可以用来模拟发送NoticeRoundStart消息，测试前端计时器更新功能
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameMgr_1 = require("../common/GameMgr");
var EventCenter_1 = require("../common/EventCenter");
var MessageId_1 = require("../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var NoticeRoundStartTest = /** @class */ (function (_super) {
    __extends(NoticeRoundStartTest, _super);
    function NoticeRoundStartTest() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.testButton = null;
        _this.firstChoiceTestButton = null;
        _this.testGameStartAnimationBtn = null;
        _this.testRoundStartAnimationBtn = null;
        _this.testAIStatusChangeBtn = null;
        _this.statusLabel = null;
        return _this;
    }
    NoticeRoundStartTest.prototype.start = function () {
        if (this.testButton) {
            this.testButton.node.on('click', this.sendTestMessage, this);
        }
        if (this.firstChoiceTestButton) {
            this.firstChoiceTestButton.node.on('click', this.testFirstChoiceBonusFlow, this);
        }
        if (this.testGameStartAnimationBtn) {
            this.testGameStartAnimationBtn.node.on('click', this.testGameStartAnimation, this);
        }
        if (this.testRoundStartAnimationBtn) {
            this.testRoundStartAnimationBtn.node.on('click', this.testRoundStartAnimation, this);
        }
        if (this.statusLabel) {
            this.statusLabel.string = '点击按钮测试消息';
        }
    };
    // 发送测试的NoticeRoundStart消息
    NoticeRoundStartTest.prototype.sendTestMessage = function () {
        // 创建测试数据
        var testData = {
            roundNumber: 1,
            countDown: 25,
            gameStatus: 0
        };
        // 模拟接收到的消息格式
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeRoundStart,
            code: 0,
            msg: "success",
            data: testData
        };
        // 发送消息事件
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001\u6D4B\u8BD5\u6D88\u606F: \u56DE\u5408" + testData.roundNumber + ", \u5012\u8BA1\u65F6" + testData.countDown + "\u79D2";
        }
    };
    // 发送倒计时更新测试
    NoticeRoundStartTest.prototype.sendCountdownUpdate = function (seconds) {
        var testData = {
            roundNumber: 1,
            countDown: seconds,
            gameStatus: 0
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeRoundStart,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5012\u8BA1\u65F6\u66F4\u65B0: " + seconds + "\u79D2";
        }
    };
    // 测试不同的倒计时值
    NoticeRoundStartTest.prototype.testDifferentCountdowns = function () {
        var _this = this;
        // 测试25秒倒计时
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(25);
        }, 1);
        // 测试20秒倒计时（进入展示阶段）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(20);
        }, 3);
        // 测试5秒倒计时（回合结束前）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(5);
        }, 5);
        // 测试0秒倒计时（回合结束）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(0);
        }, 7);
    };
    // 发送NoticeActionDisplay测试消息
    NoticeRoundStartTest.prototype.sendActionDisplayMessage = function () {
        var testData = {
            roundNumber: 1,
            gameStatus: 0,
            countDown: 5,
            playerActions: [
                {
                    userId: "player_001",
                    x: 3,
                    y: 2,
                    action: 1,
                    score: 1,
                    isFirstChoice: true,
                    result: 2 // 数字2
                },
                {
                    userId: "player_002",
                    x: 1,
                    y: 4,
                    action: 2,
                    score: 1,
                    isFirstChoice: false,
                    result: "correct_mark"
                }
            ],
            playerTotalScores: {
                "player_001": 5,
                "player_002": 3
            },
            remainingMines: 10,
            message: "展示阶段：显示所有玩家操作"
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeActionDisplay,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001ActionDisplay\u6D88\u606F: \u5C55\u793A\u9636\u6BB5\uFF0C\u5269\u4F59" + testData.countDown + "\u79D2\uFF0C\u5269\u4F59\u70B8\u5F39" + testData.remainingMines + "\u4E2A";
        }
    };
    // 测试完整的回合流程
    NoticeRoundStartTest.prototype.testFullRoundFlow = function () {
        var _this = this;
        // 1. 发送回合开始
        this.sendTestMessage();
        // 2. 20秒后发送操作展示
        this.scheduleOnce(function () {
            _this.sendActionDisplayMessage();
        }, 2);
        if (this.statusLabel) {
            this.statusLabel.string = '开始测试完整回合流程...';
        }
    };
    // 测试先手奖励和后续加分流程
    NoticeRoundStartTest.prototype.testFirstChoiceBonusFlow = function () {
        var _this = this;
        // 1. 发送回合开始
        this.sendTestMessage();
        // 2. 2秒后发送先手奖励
        this.scheduleOnce(function () {
            _this.sendFirstChoiceBonusMessage();
        }, 2);
        // 3. 4秒后发送操作展示（包含先手玩家）
        this.scheduleOnce(function () {
            _this.sendActionDisplayWithFirstChoiceMessage();
        }, 4);
        if (this.statusLabel) {
            this.statusLabel.string = '测试先手奖励+本回合加分...';
        }
    };
    // 发送NoticeFirstChoiceBonus测试消息
    NoticeRoundStartTest.prototype.sendFirstChoiceBonusMessage = function () {
        var testData = {
            userId: "player_001",
            roundNumber: 1,
            bonusScore: 1,
            totalScore: 6 // 原来5分 + 1分先手奖励
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeFirstChoiceBonus,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001FirstChoiceBonus: player_001\u83B7\u5F97+1\u5148\u624B\u5956\u52B1";
        }
    };
    // 发送包含先手玩家的NoticeActionDisplay测试消息
    NoticeRoundStartTest.prototype.sendActionDisplayWithFirstChoiceMessage = function () {
        var testData = {
            roundNumber: 1,
            gameStatus: 0,
            countDown: 5,
            playerActions: [
                {
                    userId: "player_001",
                    x: 3,
                    y: 2,
                    action: 1,
                    score: 2,
                    isFirstChoice: true,
                    result: 3 // 数字3
                },
                {
                    userId: "player_002",
                    x: 1,
                    y: 4,
                    action: 2,
                    score: 1,
                    isFirstChoice: false,
                    result: "correct_mark"
                }
            ],
            playerTotalScores: {
                "player_001": 8,
                "player_002": 4 // 原来3分 + 本回合1分
            },
            remainingMines: 9,
            message: "展示阶段：先手玩家应该显示两次player_game_pfb分数变化"
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeActionDisplay,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001ActionDisplay: \u5148\u624B\u73A9\u5BB6\u5E94\u663E\u793A+2\u5206";
        }
    };
    /**
     * 测试游戏开始动画
     */
    NoticeRoundStartTest.prototype.testGameStartAnimation = function () {
        var _this = this;
        if (this.statusLabel) {
            this.statusLabel.string = "测试游戏开始动画...";
        }
        // 获取GamePageController实例
        var gamePageController = window.gamePageController;
        if (gamePageController) {
            // 调用游戏开始动画
            if (gamePageController.showGameStartAnimation) {
                gamePageController.showGameStartAnimation();
                if (this.statusLabel) {
                    this.statusLabel.string = "游戏开始动画已触发";
                }
                // 3秒后隐藏
                this.scheduleOnce(function () {
                    if (gamePageController.hideGameStartAnimation) {
                        gamePageController.hideGameStartAnimation();
                        if (_this.statusLabel) {
                            _this.statusLabel.string = "游戏开始动画已隐藏";
                        }
                    }
                }, 3);
            }
            else {
                if (this.statusLabel) {
                    this.statusLabel.string = "GamePageController中没有找到showGameStartAnimation方法";
                }
            }
        }
        else {
            if (this.statusLabel) {
                this.statusLabel.string = "未找到GamePageController实例";
            }
        }
    };
    /**
     * 测试回合开始动画
     */
    NoticeRoundStartTest.prototype.testRoundStartAnimation = function () {
        if (this.statusLabel) {
            this.statusLabel.string = "测试回合开始动画...";
        }
        // 获取GamePageController实例
        var gamePageController = window.gamePageController;
        if (gamePageController) {
            // 调用回合开始动画
            if (gamePageController.showRoundStartAnimation) {
                gamePageController.showRoundStartAnimation();
                if (this.statusLabel) {
                    this.statusLabel.string = "回合开始动画已触发";
                }
            }
            else {
                if (this.statusLabel) {
                    this.statusLabel.string = "GamePageController中没有找到showRoundStartAnimation方法";
                }
            }
        }
        else {
            if (this.statusLabel) {
                this.statusLabel.string = "未找到GamePageController实例";
            }
        }
    };
    NoticeRoundStartTest.prototype.onDestroy = function () {
        if (this.testButton) {
            this.testButton.node.off('click', this.sendTestMessage, this);
        }
        if (this.firstChoiceTestButton) {
            this.firstChoiceTestButton.node.off('click', this.testFirstChoiceBonusFlow, this);
        }
        if (this.testGameStartAnimationBtn) {
            this.testGameStartAnimationBtn.node.off('click', this.testGameStartAnimation, this);
        }
        if (this.testRoundStartAnimationBtn) {
            this.testRoundStartAnimationBtn.node.off('click', this.testRoundStartAnimation, this);
        }
    };
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testButton", void 0);
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "firstChoiceTestButton", void 0);
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testGameStartAnimationBtn", void 0);
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testRoundStartAnimationBtn", void 0);
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testAIStatusChangeBtn", void 0);
    __decorate([
        property(cc.Label)
    ], NoticeRoundStartTest.prototype, "statusLabel", void 0);
    NoticeRoundStartTest = __decorate([
        ccclass
    ], NoticeRoundStartTest);
    return NoticeRoundStartTest;
}(cc.Component));
exports.default = NoticeRoundStartTest;

cc._RF.pop();