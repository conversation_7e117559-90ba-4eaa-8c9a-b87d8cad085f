{"version": 3, "sources": ["assets/scripts/game/Chess/HexChessBoardController.ts"], "names": [], "mappings": ";;;;;AAAA,oBAAoB;AACpB,4EAA4E;AAC5E,mBAAmB;AACnB,sFAAsF;AACtF,8BAA8B;AAC9B,sFAAsF;;;;;;;;;;;;;;;;;;;;;AAGtF,oDAAmD;AACnD,wEAAmE;AAE7D,IAAA,KAAsB,EAAE,CAAC,UAAU,EAAlC,OAAO,aAAA,EAAE,QAAQ,cAAiB,CAAC;AAY1C;IAAqD,2CAAY;IAAjE;QAAA,qEA80DC;QA30DG,sBAAgB,GAAc,IAAI,CAAC;QAGnC,gBAAU,GAAc,IAAI,CAAC;QAG7B,kBAAY,GAAc,IAAI,CAAC;QAG/B,iBAAW,GAAc,IAAI,CAAC;QAG9B,iBAAW,GAAc,IAAI,CAAC;QAG9B,iBAAW,GAAc,IAAI,CAAC;QAG9B,iBAAW,GAAc,IAAI,CAAC;QAG9B,iBAAW,GAAc,IAAI,CAAC;QAG9B,iBAAW,GAAc,IAAI,CAAC;QAG9B,iBAAW,GAAc,IAAI,CAAC;QAG9B,iBAAW,GAAc,IAAI,CAAC;QAG9B,eAAS,GAAY,IAAI,CAAC,CAAE,OAAO;QAEnC,UAAU;QACO,cAAQ,GAAG,EAAE,CAAC,CAAE,QAAQ;QACxB,eAAS,GAAG,KAAI,CAAC,QAAQ,GAAG,CAAC,CAAC,CAAE,QAAQ;QACxC,gBAAU,GAAG,KAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAE,QAAQ;QAErE,wBAAwB;QAChB,iBAAW,GAA6B,IAAI,GAAG,EAAE,CAAC,CAAE,YAAY;QAChE,kBAAY,GAAyB,IAAI,GAAG,EAAE,CAAC,CAAE,YAAY;QAC7D,oBAAc,GAAe,EAAE,CAAC,CAAE,aAAa;;IAgyD3D,CAAC;IA9xDG,wCAAM,GAAN;IAEA,CAAC;IAED,uCAAK,GAAL;QAAA,iBAWC;QAVG,+BAA+B;QAC/B,IAAI,CAAC,YAAY,CAAC;YAEd,KAAI,CAAC,iBAAiB,CAAC,EAAE,CAAC,CAAC,CAAE,cAAc;YAE3C,YAAY;YACZ,KAAI,CAAC,0BAA0B,EAAE,CAAC;YAElC,KAAI,CAAC,2BAA2B,EAAE,CAAC;QACvC,CAAC,EAAE,GAAG,CAAC,CAAC;IACZ,CAAC;IAED;;;OAGG;IACI,mDAAiB,GAAxB,UAAyB,OAAmB;QAGxC,yBAAyB;QACzB,IAAI,CAAC,2BAA2B,EAAE,CAAC;QAEnC,IAAI,CAAC,YAAY,EAAE,CAAC;IACxB,CAAC;IAED;;OAEG;IACK,6DAA2B,GAAnC;QACI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,oBAAoB,CAAC,CAAC;YACpC,OAAO;SACV;QAED,IAAM,WAAW,GAAe,EAAE,CAAC;QACnC,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;gCAIhC,CAAC;YACN,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAM,MAAM,GAAG,OAAK,0BAA0B,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YAE3D,IAAI,MAAM,EAAE;gBACR,gBAAgB;gBAChB,IAAM,MAAM,GAAG,WAAW,CAAC,IAAI,CAAC,UAAA,CAAC,IAAI,OAAA,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,KAAK,MAAM,CAAC,CAAC,EAApC,CAAoC,CAAC,CAAC;gBAC3E,IAAI,CAAC,MAAM,EAAE;oBACT,WAAW,CAAC,IAAI,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;iBAElD;aACJ;;;QAXL,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE;oBAA/B,CAAC;SAYT;QAED,IAAI,CAAC,cAAc,GAAG,WAAW,CAAC;IAEtC,CAAC;IAED,WAAW;IACH,8CAAY,GAApB;QACI,SAAS;QACT,IAAI,CAAC,WAAW,CAAC,KAAK,EAAE,CAAC;QACzB,IAAI,CAAC,YAAY,CAAC,KAAK,EAAE,CAAC;QAE1B,aAAa;QACb,KAAoB,UAAmB,EAAnB,KAAA,IAAI,CAAC,cAAc,EAAnB,cAAmB,EAAnB,IAAmB,EAAE;YAApC,IAAM,KAAK,SAAA;YACZ,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;YAC7C,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE;gBACtB,CAAC,EAAE,KAAK,CAAC,CAAC;gBACV,CAAC,EAAE,KAAK,CAAC,CAAC;gBACV,QAAQ,EAAE,IAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;gBACpD,SAAS,EAAE,KAAK;aACnB,CAAC,CAAC;SACN;QAED,IAAI,CAAC,kBAAkB,EAAE,CAAC;IAC9B,CAAC;IAED,cAAc;IACN,2CAAS,GAAjB,UAAkB,CAAS,EAAE,CAAS;QAClC,OAAU,CAAC,SAAI,CAAG,CAAC;IACvB,CAAC;IAED,cAAc;IACN,oDAAkB,GAA1B;QACI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,UAAU,CAAC,CAAC;YAC1B,OAAO;SACV;QAED,oBAAoB;QACpB,IAAI,CAAC,2BAA2B,EAAE,CAAC;IACvC,CAAC;IAED,cAAc;IACN,6DAA2B,GAAnC;QACI,aAAa;QACb,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACnC,OAAO;SACV;QAED,eAAe;QACf,IAAI,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QAEvC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAI,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAExB,iBAAiB;YACjB,IAAI,MAAM,GAAG,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,IAAI,CAAC,CAAC;YACzD,IAAI,MAAM,EAAE;gBACR,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;gBACxD,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,CAAC,EAAE,MAAM,CAAC,CAAC,CAAC,CAAC;gBAC/C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;aACrC;iBAAM;gBACH,oBAAoB;gBACpB,IAAI,GAAG,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;gBAC9B,IAAI,QAAM,GAAG,IAAI,CAAC,4BAA4B,CAAC,GAAG,CAAC,CAAC;gBACpD,IAAI,QAAM,EAAE;oBACR,IAAI,CAAC,uBAAuB,CAAC,KAAK,EAAE,QAAM,CAAC,CAAC,EAAE,QAAM,CAAC,CAAC,CAAC,CAAC;oBACxD,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,QAAM,CAAC,CAAC,EAAE,QAAM,CAAC,CAAC,CAAC,CAAC;oBAC/C,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,EAAE,KAAK,CAAC,CAAC;iBACrC;aACJ;SACJ;IACL,CAAC;IAED,eAAe;IACP,4DAA0B,GAAlC,UAAmC,QAAgB;QAI/C,IAAM,QAAQ,GAAG;YACb,4BAA4B;SAC/B,CAAC;QAEF,KAAsB,UAAQ,EAAR,qBAAQ,EAAR,sBAAQ,EAAR,IAAQ,EAAE;YAA3B,IAAM,OAAO,iBAAA;YACd,IAAM,KAAK,GAAG,QAAQ,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC;YACtC,IAAI,KAAK,EAAE;gBACP,IAAM,MAAM,GAAG,EAAC,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,EAAC,CAAC;gBAE9D,OAAO,MAAM,CAAC;aACjB;SACJ;QAED,OAAO,CAAC,IAAI,CAAC,8DAAe,QAAU,CAAC,CAAC;QACxC,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,iBAAiB;IACT,8DAA4B,GAApC,UAAqC,GAAY;QAC7C,uBAAuB;QACvB,IAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QAChB,IAAM,CAAC,GAAG,GAAG,CAAC,CAAC,CAAC;QAEhB,cAAc;QACd,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC,GAAC,CAAC,GAAG,CAAC,GAAG,CAAC,GAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QACrE,IAAM,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC,GAAC,CAAC,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,QAAQ,CAAC,CAAC;QAEhD,YAAY;QACZ,IAAI,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YACjC,OAAO,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAC,CAAC;SACvB;QACD,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,gCAAgC;IACxB,qDAAmB,GAA3B,UAA4B,CAAS,EAAE,CAAS,EAAE,cAA+B;QAA/B,+BAAA,EAAA,sBAA+B;QAC7E,eAAe;QACf,IAAM,WAAW,GAAG,IAAI,GAAG,EAAmB,CAAC;QAC/C,0BAA0B;QAC1B,WAAW,CAAC,GAAG,CAAC,KAAK,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAG,UAAU;QACvD,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,WAAW;QACxD,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAE,WAAW;QACxD,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAG,WAAW;QACxD,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,EAAE,CAAC,CAAC,CAAC,CAAI,WAAW;QACxD,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAG,WAAW;QACxD,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAG,WAAW;QACxD,WAAW,CAAC,GAAG,CAAC,MAAM,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC,CAAG,WAAW;QAExD,cAAc;QACd,IAAM,GAAG,GAAM,CAAC,SAAI,CAAG,CAAC;QACxB,IAAI,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE;YACtB,IAAM,GAAG,GAAG,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;YACjC,sBAAsB;YACtB,IAAI,cAAc,EAAE;gBAChB,OAAO,EAAE,CAAC,EAAE,CAAC,GAAG,CAAC,CAAC,EAAG,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,aAAa;aAClD;YACD,OAAO,GAAG,CAAC;SACd;QAED,4BAA4B;QAE5B,2BAA2B;QAC3B,IAAM,cAAc,GAAG,EAAE,CAAC,CAAC,WAAW;QACtC,IAAM,OAAO,GAAG,IAAI,GAAG,EAAqD,CAAC;QAE7E,sBAAsB;QACtB,OAAO,CAAC,GAAG,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAG,+BAA+B;QACrF,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAE,iCAAiC;QACvF,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAE,iCAAiC;QACvF,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,CAAC,CAAG,gCAAgC;QACtF,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,CAAC,CAAI,+BAA+B;QACrF,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAG,gCAAgC;QACtF,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAG,gCAAgC;QACtF,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,EAAE,KAAK,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,CAAC,CAAC,CAAG,gCAAgC;QAEtF,SAAS;QACT,IAAI,CAAS,EAAE,CAAS,CAAC;QAEzB,oBAAoB;QACpB,IAAI,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE;YAChB,IAAM,IAAI,GAAG,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC;YAC5B,CAAC,GAAG,IAAI,CAAC,KAAK,GAAG,CAAC,CAAC,GAAG,IAAI,CAAC,KAAK,CAAC,GAAG,cAAc,CAAC;YACnD,CAAC,GAAG,IAAI,CAAC,CAAC,CAAC;SACd;aAAM;YACH,iCAAiC;YACjC,IAAM,KAAK,GAAG,CAAC,GAAG,CAAC,CAAE,WAAW;YAChC,IAAM,KAAK,GAAG,CAAC,GAAG,CAAC;YACnB,IAAM,MAAM,GAAG,CAAC,EAAE,CAAC;YACnB,IAAM,MAAM,GAAG,EAAE,CAAC;YAElB,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC,GAAG,MAAM,CAAC;YAC5C,CAAC,GAAG,KAAK,GAAG,CAAC,GAAG,MAAM,CAAC;SAC1B;QAED,sBAAsB;QACtB,IAAI,cAAc,EAAE;YAChB,CAAC,IAAI,EAAE,CAAC,CAAC,uCAAuC;SACnD;QAED,OAAO,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IACvB,CAAC;IAED,iBAAiB;IACT,yDAAuB,GAA/B,UAAgC,QAAiB,EAAE,CAAS,EAAE,CAAS;QAAvE,iBA8DC;QA7DG,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAClC,OAAO,CAAC,KAAK,CAAC,gFAAsC,CAAC,SAAI,CAAC,0CAAS,CAAC,CAAC;YACrE,OAAO;SACV;QAED,SAAS;QACT,IAAI,cAAc,GAAG,KAAK,CAAC;QAC3B,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAI,iBAAiB,GAAa,IAAI,CAAC;QACvC,IAAM,eAAe,GAAG,GAAG,CAAC,CAAC,SAAS;QAEtC,SAAS;QACT,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,UAAC,MAA2B;YACnE,cAAc,GAAG,IAAI,CAAC;YACtB,cAAc,GAAG,CAAC,CAAC;YAEnB,SAAS;YACT,iBAAiB,GAAG;gBAChB,IAAI,cAAc,EAAE;oBAChB,cAAc,IAAI,GAAG,CAAC;oBACtB,IAAI,cAAc,IAAI,eAAe,EAAE;wBACnC,KAAI,CAAC,kBAAkB,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;wBAC9B,cAAc,GAAG,KAAK,CAAC;wBACvB,IAAI,iBAAiB,EAAE;4BACnB,KAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;yBACtC;qBACJ;iBACJ;YACL,CAAC,CAAC;YACF,KAAI,CAAC,QAAQ,CAAC,iBAAiB,EAAE,GAAG,CAAC,CAAC;QAC1C,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,SAAS;QACT,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,SAAS,EAAE,UAAC,KAA0B;YAChE,iBAAiB;YACjB,IAAI,cAAc,IAAI,cAAc,GAAG,eAAe,EAAE;gBACpD,KAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;aACpC;YAED,cAAc,GAAG,KAAK,CAAC;YACvB,IAAI,iBAAiB,EAAE;gBACnB,KAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;aACtC;QACL,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,SAAS;QACT,QAAQ,CAAC,EAAE,CAAC,EAAE,CAAC,IAAI,CAAC,SAAS,CAAC,YAAY,EAAE,UAAC,MAA2B;YACpE,cAAc,GAAG,KAAK,CAAC;YACvB,IAAI,iBAAiB,EAAE;gBACnB,KAAI,CAAC,UAAU,CAAC,iBAAiB,CAAC,CAAC;aACtC;QACL,CAAC,EAAE,IAAI,CAAC,CAAC;QAET,oBAAoB;QACpB,IAAI,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QAC9C,IAAI,CAAC,MAAM,EAAE;YACT,MAAM,GAAG,QAAQ,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;YAC1C,MAAM,CAAC,UAAU,GAAG,EAAE,CAAC,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC;YAC/C,MAAM,CAAC,SAAS,GAAG,IAAI,CAAC;SAC3B;IACL,CAAC;IAED,qBAAqB;IACb,gDAAc,GAAtB,UAAuB,CAAS,EAAE,CAAS,EAAE,MAA4B;QAGrE,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAClC,OAAO,CAAC,IAAI,CAAC,+DAAgB,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;YACzC,OAAO;SACV;QAED,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE3C,kBAAkB;QAClB,IAAI,QAAQ,IAAI,QAAQ,CAAC,SAAS,EAAE;YAChC,OAAO,CAAC,IAAI,CAAC,+BAAS,CAAC,UAAK,CAAC,8BAAO,CAAC,CAAC;YACtC,OAAO;SACV;QAID,wBAAwB;QACxB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACpC,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,MAAM,EAAE,CAAC,CAAE,SAAS;SACvB,CAAC,CAAC;IACP,CAAC;IAED,qBAAqB;IACb,oDAAkB,GAA1B,UAA2B,CAAS,EAAE,CAAS;QAG3C,WAAW;QACX,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAClC,OAAO,CAAC,IAAI,CAAC,+DAAgB,CAAC,UAAK,CAAC,MAAG,CAAC,CAAC;YACzC,OAAO;SACV;QAED,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE3C,kBAAkB;QAClB,IAAI,QAAQ,IAAI,QAAQ,CAAC,SAAS,EAAE;YAChC,OAAO,CAAC,IAAI,CAAC,+BAAS,CAAC,UAAK,CAAC,8BAAO,CAAC,CAAC;YACtC,OAAO;SACV;QAID,wBAAwB;QACxB,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,uBAAuB,EAAE;YACpC,CAAC,EAAE,CAAC;YACJ,CAAC,EAAE,CAAC;YACJ,MAAM,EAAE,CAAC,CAAE,SAAS;SACvB,CAAC,CAAC;IACP,CAAC;IAED,cAAc;IACP,sDAAoB,GAA3B,UAA4B,CAAS,EAAE,CAAS;QAC5C,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;IACrC,CAAC;IAED,iBAAiB;IACV,sDAAoB,GAA3B,UAA4B,CAAS,EAAE,CAAS,EAAE,QAAyB;QAA3E,iBAmDC;QAnDiD,yBAAA,EAAA,gBAAyB;QACvE,cAAc;QACd,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAClC,OAAO,CAAC,KAAK,CAAC,2DAAgC,CAAC,SAAI,CAAC,MAAG,CAAC,CAAC;YACzD,OAAO;SACV;QAED,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE3C,cAAc;QACd,IAAI,CAAC,QAAQ,IAAI,QAAQ,CAAC,SAAS,EAAE;YACjC,OAAO,CAAC,KAAK,CAAC,+CAA8B,CAAC,SAAI,CAAC,wEAAc,CAAC,CAAC;YAClE,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,gBAAgB,EAAE;YACxB,OAAO,CAAC,KAAK,CAAC,aAAa,CAAC,CAAC;YAC7B,OAAO;SACV;QAED,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;YAC5B,OAAO;SACV;QAED,WAAW;QACX,IAAI,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAEvD,yBAAyB;QACzB,IAAI,eAAe,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;QAC3D,UAAU,CAAC,WAAW,CAAC,eAAe,CAAC,CAAC;QAExC,gBAAgB;QAChB,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;QAEzB,oBAAoB;QACpB,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC;QAE1B,eAAe;QACf,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAErC,kBAAkB;QAClB,IAAI,CAAC,sBAAsB,CAAC,UAAU,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE;YACpD,mBAAmB;YACnB,KAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;QAEH,SAAS;QACT,QAAQ,CAAC,SAAS,GAAG,IAAI,CAAC;QAC1B,QAAQ,CAAC,UAAU,GAAG,UAAU,CAAC;IACrC,CAAC;IAED,wBAAwB;IAChB,qDAAmB,GAA3B,UAA4B,UAAmB;QAC3C,aAAa;QACb,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,mBAAmB,CAAC,CAAC;YACnC,OAAO;SACV;QAED,oBAAoB;QACpB,IAAI,MAAM,GAAG,IAAI,CAAC,SAAS,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;QACpD,IAAI,MAAM,EAAE;YACR,aAAa;YACb,MAAM,CAAC,OAAO,GAAG,KAAK,CAAC;YACvB,OAAO;YACP,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SACvC;aAAM;YACH,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;SACvC;IACL,CAAC;IAED,gBAAgB;IACR,wDAAsB,GAA9B,UAA+B,UAAmB,EAAE,CAAS,EAAE,CAAS,EAAE,QAAiB,EAAE,UAAsB;;QAC/G,kCAAkC;QAClC,IAAI,gBAAgB,GAAG,UAAU,CAAC,YAAY,CAAC,8BAAoB,CAAC,CAAC;QAErE,IAAI,gBAAgB,EAAE;YAClB,iBAAiB;YACjB,IAAI,gBAAgB,CAAC,MAAM,EAAE;gBACzB,wBAAwB;gBACxB,IAAI,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;gBACnE,IAAI,CAAC,YAAY,EAAE;oBACf,YAAY,GAAG,gBAAgB,CAAC,MAAM,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;iBAClE;gBAED,eAAe;gBACf,gBAAgB,CAAC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;gBACtC,gBAAgB,CAAC,MAAM,CAAC,OAAO,GAAG,GAAG,CAAC;aACzC;iBAAM;gBACH,OAAO,CAAC,KAAK,CAAC,uCAAuC,CAAC,CAAC;gBACvD,UAAU,EAAE,CAAC;gBACb,OAAO;aACV;YAED,cAAc;YACd,IAAI,gBAAgB,CAAC,QAAQ,EAAE;gBAC3B,gBAAgB,CAAC,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC;gBAE5C,eAAe;gBACf,IAAI,QAAQ,EAAE;oBACV,gBAAgB,CAAC,QAAQ,CAAC,OAAO,GAAG,GAAG,CAAC;oBAExC,kBAAkB;oBAClB,IAAI,MAAM,GAAG,gBAAgB,CAAC,QAAQ,CAAC,MAAM,CAAC;oBAC9C,OAAO,MAAM,IAAI,MAAM,KAAK,UAAU,EAAE;wBACpC,MAAM,CAAC,MAAM,GAAG,IAAI,CAAC;wBACrB,MAAM,GAAG,MAAM,CAAC,MAAM,CAAC;qBAC1B;iBACJ;aACJ;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,8DAAe,CAAC,SAAI,CAAC,MAAG,CAAC,CAAC;aAC1C;YAED,WAAW;YACX,IAAM,aAAa,GAAG,aAAA,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,0CAAE,QAAQ,0CAAE,MAAM,KAAI,gBAAc,CAAC,SAAI,CAAG,CAAC;YAErG,sBAAsB;YACtB,UAAU,CAAC,QAAQ,CAAC,GAAG,aAAa,CAAC;YAErC,cAAc;YACd,IAAI,QAAQ,GAAG;gBACX,MAAM,EAAE,aAAa;gBACrB,QAAQ,EAAE,kBAAM,CAAC,SAAI,CAAC,MAAG;gBACzB,MAAM,EAAE,IAAI,CAAC,mBAAmB,EAAE;gBAClC,KAAK,EAAE,CAAC;gBACR,GAAG,EAAE,CAAC;gBACN,IAAI,EAAE,CAAC;gBACP,MAAM,EAAE,CAAC;gBACT,IAAI,EAAE,CAAC;aACE,CAAC;YAEd,wCAAwC;YACxC,IAAI;gBACA,gBAAgB,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;gBAEnC,wCAAwC;gBACxC,IAAI,CAAC,YAAY,CAAC;oBACd,IAAI,gBAAgB,CAAC,QAAQ,EAAE;wBAC3B,gBAAgB,CAAC,QAAQ,CAAC,MAAM,GAAG,QAAQ,CAAC;qBAC/C;oBACD,UAAU,EAAE,CAAC;gBACjB,CAAC,EAAE,GAAG,CAAC,CAAC;aACX;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,WAAW,EAAE,KAAK,CAAC,CAAC;gBAClC,UAAU,EAAE,CAAC;aAChB;SAEJ;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAC7C,UAAU,EAAE,CAAC;SAChB;IACL,CAAC;IAED,YAAY;IACJ,qDAAmB,GAA3B;QACI,aAAa;QACb,OAAO,uEAAuE,CAAC;IACnF,CAAC;IAED;;;OAGG;IACK,0DAAwB,GAAhC,UAAiC,UAAmB;QAChD,IAAI,CAAC,UAAU,EAAE;YACb,OAAO,CAAC,IAAI,CAAC,eAAe,CAAC,CAAC;YAC9B,OAAO;SACV;QAED,OAAO;QACP,UAAU,CAAC,MAAM,GAAG,IAAI,CAAC;QAEzB,oBAAoB;QACpB,IAAM,aAAa,GAAG,UAAU,CAAC,MAAM,CAAC;QACxC,IAAM,UAAU,GAAG,aAAa,GAAG,GAAG,CAAC;QACvC,UAAU,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAEhC,wBAAwB;QACxB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;aACf,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,aAAa,EAAE,MAAM,EAAE,aAAa,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAChF,KAAK,EAAE,CAAC;IACjB,CAAC;IAED,eAAe;IACR,oDAAkB,GAAzB,UAA0B,CAAS,EAAE,CAAS;QAC1C,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE3C,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,SAAS,EAAE;YAClC,OAAO,KAAK,CAAC;SAChB;QAED,SAAS;QACT,IAAI,QAAQ,CAAC,UAAU,EAAE;YACrB,QAAQ,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;YACvC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;SAC9B;QAED,OAAO;QACP,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;QAE3B,OAAO,IAAI,CAAC;IAChB,CAAC;IAED,SAAS;IACF,iDAAe,GAAtB;QACI,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,mCAAmC;QACnC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,QAAQ;YAC9B,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,UAAU,EAAE;gBAC3C,QAAQ,CAAC,UAAU,CAAC,gBAAgB,EAAE,CAAC;gBACvC,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;gBAC3B,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;gBAC3B,YAAY,EAAE,CAAC;aAClB;QACL,CAAC,CAAC,CAAC;QAEH,oBAAoB;QACpB,IAAI,IAAI,CAAC,SAAS,EAAE;YAChB,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,KAAK,EAAE,CAAC,CAAC,gBAAgB;YAClE,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;gBACtC,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;gBAC1B,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE;oBAClC,8BAA8B;oBAC9B,IAAM,gBAAgB,GAAG,KAAK,CAAC,YAAY,CAAC,8BAAoB,CAAC,CAAC;oBAClE,IAAI,gBAAgB,EAAE;wBAClB,KAAK,CAAC,gBAAgB,EAAE,CAAC;wBACzB,YAAY,EAAE,CAAC;qBAClB;iBACJ;aACJ;SACJ;IAEL,CAAC;IAED;;;;OAIG;IACI,qDAAmB,GAA1B;QACI,IAAI,CAAC,eAAe,EAAE,CAAC;IAC3B,CAAC;IAED,kBAAkB;IACX,4DAA0B,GAAjC;QACI,IAAI,WAAW,GAA6B,EAAE,CAAC;QAE/C,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,QAAQ;YAC9B,IAAI,QAAQ,CAAC,SAAS,EAAE;gBACpB,WAAW,CAAC,IAAI,CAAC,EAAC,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAE,CAAC,EAAE,QAAQ,CAAC,CAAC,EAAC,CAAC,CAAC;aACpD;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,cAAc;IACP,gDAAc,GAArB,UAAsB,CAAS,EAAE,CAAS;QACtC,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAClC,OAAO,KAAK,CAAC;SAChB;QAED,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC3C,OAAO,QAAQ,CAAC,CAAC,CAAC,CAAC,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC,KAAK,CAAC;IAClD,CAAC;IAED;;;OAGG;IACI,gDAAc,GAArB;QACI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAChC,OAAO;SACV;QAED,sBAAsB;QACtB,IAAI,CAAC,oBAAoB,EAAE,CAAC;QAE5B,UAAU;QACV,IAAI,CAAC,eAAe,EAAE,CAAC;QAEvB,YAAY;QACZ,IAAI,CAAC,wBAAwB,EAAE,CAAC;IACpC,CAAC;IAED;;OAEG;IACK,sDAAoB,GAA5B;QACI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO;SACV;QAED,IAAM,gBAAgB,GAAc,EAAE,CAAC;QAEvC,aAAa;QACb,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzC,IAAM,QAAQ,GAAG,KAAK,CAAC,IAAI,CAAC;YAE5B,yBAAyB;YACzB,IAAI,IAAI,CAAC,aAAa,CAAC,KAAK,EAAE,QAAQ,CAAC,EAAE;gBACrC,gBAAgB,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;aAChC;SACJ;QAED,YAAY;QACZ,gBAAgB,CAAC,OAAO,CAAC,UAAC,KAAK;YAC3B,KAAK,CAAC,gBAAgB,EAAE,CAAC;QAC7B,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,+CAAa,GAArB,UAAsB,IAAa,EAAE,QAAgB;QACjD,mBAAmB;QACnB,IAAI,QAAQ,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,QAAQ,KAAK,UAAU,EAAE;YAC5D,OAAO,KAAK,CAAC;SAChB;QAED,WAAW;QACX,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,OAAO,CAAC,EAAE;YAC1D,OAAO,KAAK,CAAC;SAChB;QAED,YAAY;QACZ,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;YACpD,OAAO,KAAK,CAAC;SAChB;QAED,eAAe;QACf,QAAQ;QACR,IAAI,QAAQ,KAAK,MAAM,EAAE;YACrB,OAAO,IAAI,CAAC;SACf;QAED,+BAA+B;QAC/B,IAAI,QAAQ,CAAC,KAAK,CAAC,WAAW,CAAC,EAAE;YAC7B,OAAO,IAAI,CAAC;SACf;QAED,6CAA6C;QAC7C,IAAI,QAAQ,CAAC,KAAK,CAAC,qBAAqB,CAAC,EAAE;YACvC,OAAO,IAAI,CAAC;SACf;QAED,oBAAoB;QACpB,IAAI,QAAQ,CAAC,KAAK,CAAC,oBAAoB,CAAC,EAAE;YACtC,OAAO,IAAI,CAAC;SACf;QAED,gBAAgB;QAChB,IAAI,IAAI,CAAC,YAAY,CAAC,8BAAoB,CAAC,EAAE;YACzC,OAAO,IAAI,CAAC;SACf;QAED,QAAQ;QACR,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,MAAM,CAAC;YACnF,QAAQ,KAAK,QAAQ,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YACtD,OAAO,IAAI,CAAC;SACf;QAED,UAAU;QACV,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,IAAI,QAAQ,CAAC,QAAQ,CAAC,QAAQ,CAAC,EAAE;YAC5D,OAAO,IAAI,CAAC;SACf;QAED,iBAAiB;QACjB,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,iDAAe,GAAvB;QACI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO;SACV;QAED,sBAAsB;QACtB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEzC,cAAc;YACd,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,UAAU,CAAC,IAAI,KAAK,CAAC,IAAI,KAAK,UAAU,EAAE;gBAChE,gBAAgB;gBAChB,KAAK,CAAC,cAAc,EAAE,CAAC;gBAEvB,SAAS;gBACT,KAAK,CAAC,MAAM,GAAG,IAAI,CAAC;gBACpB,KAAK,CAAC,OAAO,GAAG,GAAG,CAAC;gBACpB,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;gBACjB,KAAK,CAAC,MAAM,GAAG,CAAC,CAAC;gBAEjB,WAAW;gBACX,IAAM,MAAM,GAAG,KAAK,CAAC,YAAY,CAAC,EAAE,CAAC,MAAM,CAAC,CAAC;gBAC7C,IAAI,MAAM,EAAE;oBACR,MAAM,CAAC,OAAO,GAAG,IAAI,CAAC;iBACzB;aACJ;SACJ;IACL,CAAC;IAED;;;;;OAKG;IACI,+CAAa,GAApB,UAAqB,CAAS,EAAE,CAAS,EAAE,SAA0B;QAA1B,0BAAA,EAAA,iBAA0B;QACjE,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;YAClC,OAAO,CAAC,IAAI,CAAC,4DAAa,CAAC,UAAK,CAAC,kBAAK,CAAC,CAAC;YACxC,OAAO;SACV;QAED,SAAS;QACT,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC5C,IAAI,QAAQ,EAAE;YACV,IAAI,SAAS,EAAE;gBACX,aAAa;gBACb,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;aAC3B;iBAAM;gBACH,WAAW;gBACX,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;qBACb,EAAE,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,MAAM,EAAE,CAAC,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;qBACnE,IAAI,CAAC;oBACF,QAAQ,CAAC,MAAM,GAAG,KAAK,CAAC;gBAC5B,CAAC,CAAC;qBACD,KAAK,EAAE,CAAC;aAChB;SACJ;IACL,CAAC;IAED;;OAEG;IACK,0DAAwB,GAAhC;QACI,sBAAsB;QACtB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,QAAQ;YAC9B,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;YAC3B,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;QAC/B,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,gDAAc,GAArB,UAAsB,CAAS,EAAE,CAAS;QACtC,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,OAAO,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,IAAI,IAAI,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,sDAAoB,GAA3B,UAA4B,WAAqC;QAAjE,iBAMC;QALG,WAAW,CAAC,OAAO,CAAC,UAAA,KAAK;YACrB,IAAI,KAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,IAAI,KAAI,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,EAAE;gBACtF,KAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;aAC/C;QACL,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACI,8CAAY,GAAnB,UAAoB,CAAS,EAAE,CAAS;QACpC,IAAI,CAAC,cAAc,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACI,iDAAe,GAAtB;QACI,IAAI,IAAI,GAAG;YACP,mBAAmB,EAAE,IAAI,CAAC,cAAc,CAAC,MAAM;YAC/C,iBAAiB,EAAE,IAAI,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC;YACtE,WAAW,EAAE,IAAI,CAAC,0BAA0B,EAAE,CAAC,MAAM;YACrD,mBAAmB,EAAE,CAAC,CAAC,IAAI,CAAC,gBAAgB;YAC5C,YAAY,EAAE,CAAC,CAAC,IAAI,CAAC,SAAS;YAC9B,eAAe,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;YACtC,gBAAgB,EAAE,IAAI,CAAC,YAAY,CAAC,IAAI;SAC3C,CAAC;QAEF,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;OAEG;IACI,iDAAe,GAAtB;QACI,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAC;IACtC,CAAC;IAED;;OAEG;IACI,yDAAuB,GAA9B;QACI,IAAM,SAAS,GAAG,IAAI,CAAC,eAAe,EAAE,CAAC;QACzC,IAAI,SAAS,KAAK,CAAC,EAAE;YACjB,OAAO,EAAE,CAAC,CAAC,MAAM;SACpB;QAED,aAAa;QACb,IAAM,SAAS,GAAG,IAAI,CAAC,KAAK,CAAC,SAAS,GAAG,IAAI,CAAC,CAAC;QAE/C,OAAO,IAAI,CAAC,GAAG,CAAC,SAAS,EAAE,CAAC,CAAC,CAAC,CAAC,SAAS;IAC5C,CAAC;IAED;;OAEG;IACI,4DAA0B,GAAjC;QAAA,iBAiDC;QA9CG,cAAc;QACd,IAAM,UAAU,GAAG;YACf,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,cAAc,EAAE;YACpE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE;YACvE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE;YACvE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE;YACtE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE;YACrE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE;YACtE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE;YACtE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,QAAQ,EAAE,EAAE,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,EAAE,GAAG,EAAE,EAAE,IAAI,EAAE,gBAAgB,EAAE;SACzE,CAAC;QAEF,IAAI,YAAY,GAAG,CAAC,CAAC;QAErB,UAAU,CAAC,OAAO,CAAC,UAAA,KAAK;YACpB,IAAM,UAAU,GAAG,KAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;YAC9D,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzD,IAAM,MAAM,GAAG,IAAI,CAAC,GAAG,CAAC,UAAU,CAAC,CAAC,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YACzD,IAAM,SAAS,GAAG,MAAM,GAAG,CAAC,IAAI,MAAM,GAAG,CAAC,CAAC,CAAC,UAAU;YAEtD,IAAI,SAAS;gBAAE,YAAY,EAAE,CAAC;QAGlC,CAAC,CAAC,CAAC;QAGH,WAAW;QAEX,IAAM,kBAAkB,GAAG;YACvB,SAAS;YACT,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE;YAC9E,UAAU;YACV,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;YAClE,UAAU;YACV,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;YACnF,UAAU;YACV,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;SACrE,CAAC;QAEF,kBAAkB,CAAC,OAAO,CAAC,UAAA,KAAK;YAC5B,IAAM,UAAU,GAAG,KAAI,CAAC,mBAAmB,CAAC,KAAK,CAAC,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC,CAAC;QAElE,CAAC,CAAC,CAAC;QAEH,YAAY;QACX,MAAc,CAAC,gBAAgB,GAAG,cAAM,OAAA,KAAI,CAAC,0BAA0B,EAAE,EAAjC,CAAiC,CAAC;IAC/E,CAAC;IAED,qEAAqE;IACrE,sCAAsC;IAEtC;;;;;OAKG;IACI,qDAAmB,GAA1B,UAA2B,CAAS,EAAE,CAAS,EAAE,aAA6B;QAA7B,8BAAA,EAAA,oBAA6B;QAC1E,IAAI,CAAC,IAAI,CAAC,UAAU,EAAE;YAClB,OAAO,CAAC,KAAK,CAAC,4BAA4B,CAAC,CAAC;YAC5C,OAAO;SACV;QAED,aAAa;QACb,IAAM,QAAQ,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACjD,QAAQ,CAAC,IAAI,GAAG,SAAS,CAAC;QAE1B,8BAA8B;QAC9B,IAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;QACvD,QAAQ,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAE/B,QAAQ;QACR,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAElC,SAAS;QACT,QAAQ,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACrB,EAAE,CAAC,KAAK,CAAC,QAAQ,CAAC;aACb,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC5D,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;aACrC,KAAK,EAAE,CAAC;QAEb,sBAAsB;QACtB,IAAI,aAAa,EAAE;YACf,IAAI,CAAC,uBAAuB,EAAE,CAAC;SAClC;IACL,CAAC;IAED;;;;OAIG;IACI,uDAAqB,GAA5B,UAA6B,CAAS,EAAE,CAAS;QAC7C,IAAI,CAAC,IAAI,CAAC,YAAY,EAAE;YACpB,OAAO,CAAC,KAAK,CAAC,8BAA8B,CAAC,CAAC;YAC9C,OAAO;SACV;QAED,eAAe;QACf,IAAM,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;QACrD,UAAU,CAAC,IAAI,GAAG,WAAW,CAAC;QAE9B,8BAA8B;QAC9B,IAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;QACvD,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEjC,QAAQ;QACR,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAEpC,SAAS;QACT,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;aACf,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC5D,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;;;;OAKG;IACI,+DAA6B,GAApC,UAAqC,CAAS,EAAE,CAAS,EAAE,aAAqB;QAC5E,WAAW;QACX,IAAI,aAAa,KAAK,CAAC,EAAE;YACrB,OAAO;SACV;QAED,gBAAgB;QAChB,IAAI,CAAC,qBAAqB,CAAC,CAAC,EAAE,CAAC,EAAE,aAAa,CAAC,CAAC;IACpD,CAAC;IAED;;;;;OAKG;IACK,uDAAqB,GAA7B,UAA8B,CAAS,EAAE,CAAS,EAAE,MAAc;QAC9D,eAAe;QACf,IAAI,MAAM,GAAc,IAAI,CAAC;QAC7B,QAAQ,MAAM,EAAE;YACZ,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC,KAAK,CAAC;gBAAE,MAAM,GAAG,IAAI,CAAC,WAAW,CAAC;gBAAC,MAAM;YACzC;gBACI,OAAO,CAAC,KAAK,CAAC,2CAAW,MAAQ,CAAC,CAAC;gBACnC,OAAO;SACd;QAED,IAAI,CAAC,MAAM,EAAE;YACT,OAAO,CAAC,KAAK,CAAC,SAAO,MAAM,sGAAwB,CAAC,CAAC;YACrD,OAAO;SACV;QAED,WAAW;QACX,IAAM,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;QAC1C,UAAU,CAAC,IAAI,GAAG,YAAU,MAAQ,CAAC;QAErC,8BAA8B;QAC9B,IAAM,QAAQ,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;QACvD,UAAU,CAAC,WAAW,CAAC,QAAQ,CAAC,CAAC;QAEjC,QAAQ;QACR,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;QAEpC,SAAS;QACT,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QACvB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;aACf,EAAE,CAAC,GAAG,EAAE,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aAC5D,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,yDAAuB,GAA/B;QACI,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO;SACV;QAED,IAAM,gBAAgB,GAAG,IAAI,CAAC,SAAS,CAAC,WAAW,EAAE,CAAC;QACtD,IAAM,aAAa,GAAG,EAAE,CAAC;QAEzB,EAAE,CAAC,KAAK,CAAC,IAAI,CAAC,SAAS,CAAC;aACnB,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,aAAa,EAAE,CAAC;aACnD,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,aAAa,EAAE,CAAC;aACnD,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,aAAa,EAAE,CAAC;aACnD,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,GAAG,aAAa,EAAE,CAAC;aACnD,EAAE,CAAC,IAAI,EAAE,EAAE,CAAC,EAAE,gBAAgB,CAAC,CAAC,EAAE,CAAC;aACnC,KAAK,EAAE,CAAC;IACjB,CAAC;IAED,qDAAqD;IACrD,mCAAmC;IAEnC;;;OAGG;IACI,mDAAiB,GAAxB,UAAyB,UAAsB;QAA/C,iBA8DC;QA7DG,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;YAClC,UAAU,EAAE,CAAC;YACb,OAAO;SACV;QAED,uBAAuB;QACvB,IAAM,WAAW,GAAc,EAAE,CAAC;QAElC,qCAAqC;QACrC,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,QAAQ;YAC9B,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,UAAU,EAAE;gBAC3C,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;aACzC;QACL,CAAC,CAAC,CAAC;QAEH,kCAAkC;QAClC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEzC,qBAAqB;YACrB,IAAM,gBAAgB,GAAG,KAAK,CAAC,YAAY,CAAC,8BAAoB,CAAC,CAAC;YAClE,IAAI,gBAAgB,EAAE;gBAClB,uBAAuB;gBACvB,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;oBAC9B,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBAC3B;aACJ;SACJ;QAID,gBAAgB;QAChB,IAAI,WAAW,CAAC,MAAM,KAAK,CAAC,EAAE;YAC1B,IAAI,CAAC,6BAA6B,EAAE,CAAC;YACrC,UAAU,EAAE,CAAC;YACb,OAAO;SACV;QAED,IAAI,cAAc,GAAG,CAAC,CAAC;QACvB,IAAM,UAAU,GAAG,WAAW,CAAC,MAAM,CAAC;QAEtC,0BAA0B;QAC1B,WAAW,CAAC,OAAO,CAAC,UAAC,UAAU;YAC3B,mBAAmB;YACnB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;iBACf,EAAE,CAAC,GAAG,EAAE,EAAE,OAAO,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,EAAE,EAAE,EAAE,MAAM,EAAE,QAAQ,EAAE,CAAC;iBACvE,IAAI,CAAC;gBACF,YAAY;gBACZ,UAAU,CAAC,gBAAgB,EAAE,CAAC;gBAC9B,cAAc,EAAE,CAAC;gBAEjB,kBAAkB;gBAClB,IAAI,cAAc,IAAI,UAAU,EAAE;oBAC9B,cAAc;oBACd,KAAI,CAAC,6BAA6B,EAAE,CAAC;oBACrC,UAAU,EAAE,CAAC;iBAChB;YACL,CAAC,CAAC;iBACD,KAAK,EAAE,CAAC;QACjB,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;OAEG;IACK,+DAA6B,GAArC;QACI,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,QAAQ;YAC9B,IAAI,QAAQ,CAAC,SAAS,EAAE;gBACpB,QAAQ,CAAC,SAAS,GAAG,KAAK,CAAC;gBAC3B,QAAQ,CAAC,UAAU,GAAG,IAAI,CAAC;aAC9B;QACL,CAAC,CAAC,CAAC;IAEP,CAAC;IAED,qDAAqD;IACrD,sBAAsB;IAEtB;;;;;;OAMG;IACI,0DAAwB,GAA/B,UAAgC,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,WAAoB;QAAzF,iBA4BC;QA3BG,aAAa;QACb,IAAM,UAAU,GAAG,IAAI,CAAC,2BAA2B,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QAC1D,IAAI,CAAC,UAAU,EAAE;YACb,2CAA2C;YAE3C,OAAO;SACV;QAED,2BAA2B;QAC3B,IAAM,gBAAgB,GAAG,UAAU,CAAC,YAAY,CAAC,8BAAoB,CAAC,CAAC;QACvE,IAAI,CAAC,gBAAgB,EAAE;YACnB,OAAO,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAC1C,OAAO;SACV;QAED,SAAS;QACT,IAAI,WAAW,EAAE;YACb,iBAAiB;YACjB,IAAI,CAAC,2BAA2B,CAAC,gBAAgB,EAAE,CAAC,EAAE;gBAClD,KAAI,CAAC,YAAY,CAAC;oBACd,KAAI,CAAC,2BAA2B,CAAC,gBAAgB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;gBACpE,CAAC,EAAE,GAAG,CAAC,CAAC;YACZ,CAAC,CAAC,CAAC;SACN;aAAM;YACH,WAAW;YACX,IAAI,CAAC,2BAA2B,CAAC,gBAAgB,EAAE,KAAK,EAAE,IAAI,CAAC,CAAC;SACnE;IACL,CAAC;IAED;;;;;OAKG;IACK,6DAA2B,GAAnC,UAAoC,CAAS,EAAE,CAAS;QACpD,8BAA8B;QAC9B,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,QAAQ,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,UAAU,EAAE;YACvD,OAAO,QAAQ,CAAC,UAAU,CAAC;SAC9B;QAED,qBAAqB;QACrB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,IAAI,CAAC;SACf;QAED,gCAAgC;QAChC,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAI,KAAK,CAAC,IAAI,KAAK,iBAAiB,EAAE;gBAClC,oBAAoB;gBACpB,IAAM,WAAW,GAAG,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;gBACzD,IAAM,SAAS,GAAG,KAAK,CAAC,WAAW,EAAE,CAAC;gBACtC,IAAM,QAAQ,GAAG,WAAW,CAAC,GAAG,CAAC,SAAS,CAAC,CAAC,GAAG,EAAE,CAAC;gBAClD,IAAI,QAAQ,GAAG,EAAE,EAAE,EAAE,YAAY;oBAC7B,OAAO,KAAK,CAAC;iBAChB;aACJ;SACJ;QAED,OAAO,IAAI,CAAC;IAChB,CAAC;IAED;;;;;OAKG;IACK,6DAA2B,GAAnC,UAAoC,gBAAqB,EAAE,KAAa,EAAE,UAA+B;QACrG,wCAAwC;QACxC,IAAI,gBAAgB,IAAI,OAAO,gBAAgB,CAAC,YAAY,KAAK,UAAU,EAAE;YACzE,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;SACxC;QAED,IAAI,UAAU,EAAE;YACZ,IAAI,CAAC,YAAY,CAAC,UAAU,EAAE,GAAG,CAAC,CAAC;SACtC;IACL,CAAC;IAED;;;;OAIG;IACI,wDAAsB,GAA7B,UAA8B,MAAc,EAAE,KAAa;QACvD,IAAM,aAAa,GAAG,IAAI,CAAC,mBAAmB,EAAE,CAAC;QACjD,IAAI,WAAW,GAAG,KAAK,CAAC;QAExB,wCAAwC;QACxC,IAAI,MAAM,KAAK,aAAa,EAAE;YAC1B,WAAW,GAAG,IAAI,CAAC,0BAA0B,CAAC,KAAK,CAAC,CAAC;SACxD;aAAM;YACH,yBAAyB;YACzB,WAAW,GAAG,IAAI,CAAC,wBAAwB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;SAC9D;QAED,IAAI,CAAC,WAAW,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,oCAAS,MAAM,gGAAkB,CAAC,CAAC;SACnD;IACL,CAAC;IAED;;OAEG;IACK,qDAAmB,GAA3B;;QACI,OAAO,aAAA,uBAAU,CAAC,WAAW,EAAE,CAAC,SAAS,0CAAE,QAAQ,0CAAE,MAAM,KAAI,EAAE,CAAC;IACtE,CAAC;IAED;;OAEG;IACK,4DAA0B,GAAlC,UAAmC,KAAa;QAAhD,iBA0BC;QAzBG,IAAI,WAAW,GAAG,KAAK,CAAC;QAExB,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,QAAQ;YAC9B,kBAAkB;YAClB,IAAI,WAAW,EAAE;gBACb,OAAO;aACV;YAED,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,UAAU,EAAE;gBAC3C,IAAI,gBAAgB,GAAG,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,sBAAsB,CAAC;oBAC1D,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,uBAAuB,CAAC,CAAC;gBAE/E,IAAI,gBAAgB,EAAE;oBAElB,KAAI,CAAC,8BAA8B,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;oBAC7D,WAAW,GAAG,IAAI,CAAC;iBACtB;aACJ;QACL,CAAC,CAAC,CAAC;QAEH,IAAI,CAAC,WAAW,EAAE;YACd,OAAO,CAAC,IAAI,CAAC,mBAAmB,CAAC,CAAC;SACrC;QAED,OAAO,WAAW,CAAC;IACvB,CAAC;IAED;;OAEG;IACK,0DAAwB,GAAhC,UAAiC,MAAc,EAAE,KAAa;QAC1D,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,KAAK,CAAC;SAChB;QAED,iBAAiB;QACjB,OAAO,IAAI,CAAC,yBAAyB,CAAC,MAAM,EAAE,KAAK,CAAC,CAAC;IACzD,CAAC;IAED;;OAEG;IACK,2DAAyB,GAAjC,UAAkC,MAAc,EAAE,KAAa;QAC3D,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,CAAC,IAAI,CAAC,0FAAkB,MAAM,wBAAM,CAAC,CAAC;YAC7C,OAAO,KAAK,CAAC;SAChB;QAED,iCAAiC;QACjC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrD,IAAM,KAAK,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEzC,iCAAiC;YACjC,IAAI,gBAAgB,GAAG,KAAK,CAAC,YAAY,CAAC,sBAAsB,CAAC,CAAC;YAClE,IAAI,CAAC,gBAAgB,EAAE;gBACnB,gBAAgB,GAAG,KAAK,CAAC,YAAY,CAAC,uBAAuB,CAAC,CAAC,CAAE,UAAU;aAC9E;YACD,IAAI,CAAC,gBAAgB,EAAE;gBACnB,WAAW;gBACX,IAAM,UAAU,GAAG,KAAK,CAAC,aAAa,CAAC,EAAE,CAAC,SAAS,CAAC,CAAC;gBACrD,gBAAgB,GAAG,UAAU,CAAC,IAAI,CAAC,UAAA,IAAI;oBACnC,OAAA,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,sBAAsB;wBAChD,IAAI,CAAC,WAAW,CAAC,IAAI,KAAK,uBAAuB;gBADjD,CACiD,CACpD,CAAC;aACL;YAED,IAAM,YAAY,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;YAErC,IAAI,YAAY,KAAK,MAAM,EAAE;gBACzB,IAAI,gBAAgB,EAAE;oBAClB,sBAAsB;oBACtB,IAAI,CAAC,8BAA8B,CAAC,gBAAgB,EAAE,KAAK,CAAC,CAAC;oBAC7D,OAAO,IAAI,CAAC;iBACf;qBAAM;oBACH,iBAAiB;oBACjB,OAAO,CAAC,IAAI,CAAC,2CAAW,MAAM,0EAA+B,CAAC,CAAC;oBAC/D,OAAO,KAAK,CAAC,CAAE,oBAAoB;iBACtC;aACJ;SACJ;QAED,OAAO,CAAC,IAAI,CAAC,2CAAW,MAAM,sDAAW,CAAC,CAAC;QAC3C,OAAO,KAAK,CAAC;IACjB,CAAC;IAED;;OAEG;IACK,gEAA8B,GAAtC,UAAuC,gBAAqB,EAAE,KAAa;QACvE,qBAAqB;QACrB,IAAM,UAAU,GAAG,gBAAgB,CAAC,IAAI,CAAC;QACzC,IAAM,oBAAoB,GAAG,UAAU,CAAC,eAAe,EAAE,CAAC;QAE1D,WAAW;QACX,UAAU,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,CAAC;QAE/B,mBAAmB;QACnB,IAAI,CAAC,0BAA0B,CAAC,gBAAgB,CAAC,CAAC;QAElD,IAAI,KAAK,GAAG,CAAC,EAAE;YACX,gBAAgB,CAAC,YAAY,CAAC,KAAK,CAAC,CAAC;SACxC;aAAM,IAAI,KAAK,GAAG,CAAC,EAAE;YAClB,gBAAgB,CAAC,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,KAAK,CAAC,CAAC,CAAC;SAClD;QAED,sBAAsB;QACtB,IAAI,CAAC,YAAY,CAAC;YACd,IAAI,UAAU,IAAI,UAAU,CAAC,OAAO,EAAE;gBAClC,UAAU,CAAC,eAAe,CAAC,oBAAoB,CAAC,CAAC;aACpD;QACL,CAAC,EAAE,GAAG,CAAC,CAAC,CAAC,mBAAmB;IAChC,CAAC;IAED;;OAEG;IACK,4DAA0B,GAAlC,UAAmC,gBAAqB;QACpD,cAAc;QACd,IAAI,gBAAgB,CAAC,YAAY,EAAE;YAC/B,gBAAgB,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC;SAClE;QAED,cAAc;QACd,IAAI,gBAAgB,CAAC,YAAY,EAAE;YAC/B,gBAAgB,CAAC,YAAY,CAAC,MAAM,GAAG,EAAE,CAAC,KAAK,CAAC,UAAU,GAAG,CAAC,CAAC;SAClE;IAGL,CAAC;IAID;;;;OAIG;IACK,+DAA6B,GAArC,UAAsC,MAAc;QAChD,IAAM,WAAW,GAAc,EAAE,CAAC;QAElC,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YACjB,OAAO,WAAW,CAAC;SACtB;QAED,cAAc;QACd,IAAM,QAAQ,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC;QACzC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,QAAQ,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACtC,IAAM,KAAK,GAAG,QAAQ,CAAC,CAAC,CAAC,CAAC;YAE1B,qBAAqB;YACrB,IAAM,gBAAgB,GAAG,KAAK,CAAC,YAAY,CAAC,8BAAoB,CAAC,CAAC;YAClE,IAAI,gBAAgB,EAAE;gBAClB,gCAAgC;gBAChC,IAAM,YAAY,GAAG,KAAK,CAAC,QAAQ,CAAC,CAAC;gBACrC,IAAI,YAAY,KAAK,MAAM,EAAE;oBACzB,WAAW,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC;iBAC3B;aACJ;SACJ;QAED,0BAA0B;QAC1B,IAAI,CAAC,WAAW,CAAC,OAAO,CAAC,UAAC,QAAQ;YAC9B,IAAI,QAAQ,CAAC,SAAS,IAAI,QAAQ,CAAC,UAAU,EAAE;gBAC3C,IAAM,gBAAgB,GAAG,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,8BAAoB,CAAC,CAAC;gBAChF,IAAM,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC;gBACnD,IAAI,gBAAgB,IAAI,YAAY,KAAK,MAAM,EAAE;oBAC7C,SAAS;oBACT,IAAI,CAAC,WAAW,CAAC,QAAQ,CAAC,QAAQ,CAAC,UAAU,CAAC,EAAE;wBAC5C,WAAW,CAAC,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC;qBACzC;iBACJ;aACJ;QACL,CAAC,CAAC,CAAC;QAEH,OAAO,WAAW,CAAC;IACvB,CAAC;IAED,qDAAqD;IACrD,gCAAgC;IAEhC;;;;;OAKG;IACI,kEAAgC,GAAvC,UAAwC,CAAS,EAAE,CAAS,EAAE,OAA8B;QACxF,IAAI,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC,EAAE,CAAC,CAAC,IAAI,CAAC,OAAO,IAAI,OAAO,CAAC,MAAM,KAAK,CAAC,EAAE;YACtE,OAAO,CAAC,IAAI,CAAC,gCAAU,CAAC,UAAK,CAAC,qBAAe,CAAA,OAAO,aAAP,OAAO,uBAAP,OAAO,CAAE,MAAM,KAAI,CAAC,CAAE,CAAC,CAAC;YACrE,OAAO;SACV;QAED,kBAAkB;QAClB,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE3C,IAAI,QAAQ,IAAI,QAAQ,CAAC,SAAS,EAAE;YAChC,yBAAyB;YACzB,IAAI,OAAO,CAAC,MAAM,GAAG,CAAC,EAAE;gBACpB,6BAA6B;gBAC7B,IAAI,CAAC,gCAAgC,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;aACxD;SACJ;aAAM;YACH,uBAAuB;YACvB,IAAI,CAAC,6BAA6B,CAAC,CAAC,EAAE,CAAC,EAAE,OAAO,CAAC,CAAC;SACrD;IACL,CAAC;IAED;;;;;;OAMG;IACK,kEAAgC,GAAxC,UAAyC,CAAS,EAAE,CAAS,EAAE,OAA8B;QACzF,wBAAwB;QACxB,IAAM,YAAY,GAAG,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC;QACxC,IAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;QAE3D,mBAAmB;QACnB,IAAM,UAAU,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC,YAAY;QAC7C,IAAI,CAAC,yBAAyB,CAAC,CAAC,EAAE,CAAC,EAAE,UAAU,EAAE,OAAO,CAAC,CAAC;QAE1D,qBAAqB;QACrB,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,iBAAiB;YAEpD,mBAAmB;YACnB,IAAI,CAAC,8BAA8B,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;SAC7E;IACL,CAAC;IAED;;;;;;OAMG;IACK,+DAA6B,GAArC,UAAsC,CAAS,EAAE,CAAS,EAAE,OAA8B;QACtF,IAAM,YAAY,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC,aAAa;QAClD,IAAM,SAAS,GAAG,IAAI,CAAC,qBAAqB,CAAC,YAAY,CAAC,CAAC;QAE3D,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,OAAO,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;YACrC,IAAM,MAAM,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;YAC1B,IAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;YAE9B,mBAAmB;YACnB,IAAI,CAAC,8BAA8B,CAAC,CAAC,EAAE,CAAC,EAAE,MAAM,EAAE,QAAQ,EAAE,YAAY,CAAC,CAAC;SAC7E;IACL,CAAC;IAID;;;;OAIG;IACK,uDAAqB,GAA7B,UAA8B,WAAmB;QAC7C,QAAQ,WAAW,EAAE;YACjB,KAAK,CAAC;gBACF,iBAAiB;gBACjB,OAAO,CAAC,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,GAAG,EAAC,CAAC,CAAC;YAEtC,KAAK,CAAC;gBACF,kBAAkB;gBAClB,OAAO;oBACH,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,EAAC;oBAC3B,EAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,KAAK,EAAE,GAAG,EAAC,CAAG,IAAI;iBACpC,CAAC;YAEN,KAAK,CAAC;gBACF,mBAAmB;gBACnB,OAAO;oBACH,EAAC,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAC;oBACzB,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,EAAC;oBAC5B,EAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,EAAC,CAAG,KAAK;iBACtC,CAAC;YAEN,KAAK,CAAC;gBACF,kBAAkB;gBAClB,OAAO;oBACH,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAC;oBAC3B,EAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,KAAK,EAAE,GAAG,EAAC;oBAC1B,EAAC,CAAC,EAAE,CAAC,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,EAAC;oBAC5B,EAAC,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,EAAE,KAAK,EAAE,GAAG,EAAC,CAAG,KAAK;iBACtC,CAAC;YAEN;gBACI,gBAAgB;gBAChB,OAAO,CAAC,IAAI,CAAC,2CAAW,WAAW,0CAAS,CAAC,CAAC;gBAC9C,OAAO,IAAI,CAAC,qBAAqB,CAAC,CAAC,CAAC,CAAC;SAC5C;IACL,CAAC;IAED;;;;;;OAMG;IACK,2DAAyB,GAAjC,UAAkC,CAAS,EAAE,CAAS,EAAE,QAA+C,EAAE,OAA8B;QACnI,IAAM,GAAG,GAAG,IAAI,CAAC,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC;QACjC,IAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAE3C,YAAY;QACZ,IAAI,CAAC,QAAQ,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,QAAQ,CAAC,UAAU,EAAE;YAC1D,OAAO,CAAC,IAAI,CAAC,0CAAU,CAAC,UAAK,CAAC,kEAAa,CAAC,CAAC;YAC7C,OAAO;SACV;QAED,IAAM,YAAY,GAAG,QAAQ,CAAC,UAAU,CAAC;QAEzC,uBAAuB;QACvB,IAAM,YAAY,GAAG,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAExD,sBAAsB;QACtB,IAAM,YAAY,GAAG,IAAI,CAAC,qCAAqC,CAAC,CAAC,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;QAEpF,WAAW;QACX,IAAM,WAAW,GAAG,EAAE,CAAC,EAAE,CACrB,YAAY,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,EAC3B,YAAY,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAC9B,CAAC;QAEF,cAAc;QACd,IAAI,CAAC,4BAA4B,CAAC,YAAY,EAAE,WAAW,EAAE,QAAQ,CAAC,KAAK,CAAC,CAAC;IACjF,CAAC;IAED;;;;;;OAMG;IACK,uEAAqC,GAA7C,UAA8C,CAAS,EAAE,CAAS,EAAE,YAAoB;QACpF,IAAI,YAAY,KAAK,CAAC,EAAE;YACpB,oCAAoC;YACpC,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;SAC/C;aAAM;YACH,+BAA+B;YAC/B,OAAO,IAAI,CAAC,mBAAmB,CAAC,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC;SAChD;IACL,CAAC;IAED;;;;;OAKG;IACK,8DAA4B,GAApC,UAAqC,UAAmB,EAAE,WAAoB,EAAE,QAAgB;QAC5F,IAAI,CAAC,UAAU,IAAI,CAAC,UAAU,CAAC,OAAO,EAAE;YACpC,OAAO;SACV;QAED,UAAU;QACV,UAAU,CAAC,cAAc,EAAE,CAAC;QAE5B,sBAAsB;QACtB,EAAE,CAAC,KAAK,CAAC,UAAU,CAAC;aACf,EAAE,CAAC,GAAG,EAAE;YACL,CAAC,EAAE,WAAW,CAAC,CAAC;YAChB,CAAC,EAAE,WAAW,CAAC,CAAC;YAChB,MAAM,EAAE,QAAQ;YAChB,MAAM,EAAE,QAAQ;SACnB,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,CAAC;aACxB,KAAK,EAAE,CAAC;IACjB,CAAC;IAED;;;;;;;OAOG;IACK,gEAA8B,GAAtC,UAAuC,CAAS,EAAE,CAAS,EAAE,MAA2B,EAAE,QAA+C,EAAE,YAAoB;QAA/J,iBAsCC;QArCG,IAAI,CAAC,IAAI,CAAC,gBAAgB,IAAI,CAAC,IAAI,CAAC,SAAS,EAAE;YAC3C,OAAO,CAAC,KAAK,CAAC,gBAAgB,CAAC,CAAC;YAChC,OAAO;SACV;QAED,WAAW;QACX,IAAI,UAAU,GAAG,EAAE,CAAC,WAAW,CAAC,IAAI,CAAC,gBAAgB,CAAC,CAAC;QAEvD,sBAAsB;QACtB,IAAM,YAAY,GAAG,IAAI,CAAC,qCAAqC,CAAC,CAAC,EAAE,CAAC,EAAE,YAAY,CAAC,CAAC;QAEpF,SAAS;QACT,IAAM,aAAa,GAAG,EAAE,CAAC,EAAE,CACvB,YAAY,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,EAC3B,YAAY,CAAC,CAAC,GAAG,QAAQ,CAAC,CAAC,CAC9B,CAAC;QAEF,UAAU,CAAC,WAAW,CAAC,aAAa,CAAC,CAAC;QAEtC,qCAAqC;QACrC,IAAI,YAAY,KAAK,CAAC,EAAE;YACpB,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,CAAC;SAC5B;aAAM;YACH,UAAU,CAAC,QAAQ,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;SACvC;QAED,oBAAoB;QACpB,UAAU,CAAC,MAAM,GAAG,KAAK,CAAC;QAE1B,QAAQ;QACR,IAAI,CAAC,mBAAmB,CAAC,UAAU,CAAC,CAAC;QAErC,eAAe;QACf,IAAI,CAAC,yBAAyB,CAAC,UAAU,EAAE,MAAM,EAAE;YAC/C,mBAAmB;YACnB,KAAI,CAAC,wBAAwB,CAAC,UAAU,CAAC,CAAC;QAC9C,CAAC,CAAC,CAAC;IACP,CAAC;IAED;;;;;OAKG;IACK,2DAAyB,GAAjC,UAAkC,UAAmB,EAAE,MAA2B,EAAE,UAAsB;QACtG,2BAA2B;QAC3B,IAAI,gBAAgB,GAAG,UAAU,CAAC,YAAY,CAAC,8BAAoB,CAAC,CAAC;QAErE,IAAI,gBAAgB,EAAE;YAClB,sBAAsB;YACtB,UAAU,CAAC,QAAQ,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC;YAErC,cAAc;YACd,IAAM,UAAQ,GAAG,CAAC,MAAM,CAAC,MAAM,KAAK,CAAC,CAAC,CAAC,CAAC,sBAAsB;YAC9D,IAAI,gBAAgB,CAAC,QAAQ,EAAE;gBAC3B,gBAAgB,CAAC,QAAQ,CAAC,MAAM,GAAG,UAAQ,CAAC;aAC/C;YAED,wBAAwB;YACxB,IAAI,YAAY,GAAG,IAAI,CAAC,eAAe,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;YACvD,IAAI,CAAC,YAAY,EAAE;gBACf,OAAO,CAAC,IAAI,CAAC,oCAAS,MAAM,CAAC,MAAM,8EAAe,CAAC,CAAC;gBACpD,aAAa;gBACb,YAAY,GAAG;oBACX,MAAM,EAAE,MAAM,CAAC,MAAM;oBACrB,QAAQ,EAAE,iBAAK,MAAM,CAAC,MAAQ;oBAC9B,MAAM,EAAE,IAAI,CAAC,mBAAmB,EAAE;oBAClC,KAAK,EAAE,CAAC;oBACR,GAAG,EAAE,CAAC;oBACN,IAAI,EAAE,CAAC;oBACP,MAAM,EAAE,CAAC;oBACT,IAAI,EAAE,CAAC;iBACE,CAAC;aACjB;YAED,wCAAwC;YACxC,IAAI;gBACA,gBAAgB,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC;gBAEvC,wCAAwC;gBACxC,IAAI,CAAC,YAAY,CAAC;oBACd,IAAI,gBAAgB,CAAC,QAAQ,EAAE;wBAC3B,gBAAgB,CAAC,QAAQ,CAAC,MAAM,GAAG,UAAQ,CAAC;qBAC/C;oBACD,UAAU,EAAE,CAAC;gBACjB,CAAC,EAAE,GAAG,CAAC,CAAC;aACX;YAAC,OAAO,KAAK,EAAE;gBACZ,OAAO,CAAC,KAAK,CAAC,eAAe,EAAE,KAAK,CAAC,CAAC;gBACtC,UAAU,EAAE,CAAC;aAChB;SACJ;aAAM;YACH,OAAO,CAAC,IAAI,CAAC,8BAA8B,CAAC,CAAC;YAC7C,UAAU,EAAE,CAAC;SAChB;IACL,CAAC;IAED;;;;OAIG;IACK,yDAAuB,GAA/B,UAAgC,MAAc;QAC1C,yBAAyB;QACzB,WAAW;QACX,OAAO,IAAI,CAAC,mBAAmB,EAAE,CAAC;IACtC,CAAC;IAMD;;;;OAIG;IACK,iDAAe,GAAvB,UAAwB,MAAc;QAClC,IAAI;YACA,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,IAAI,CAAC,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,EAAE;gBAC9F,OAAO,CAAC,IAAI,CAAC,iBAAiB,CAAC,CAAC;gBAChC,OAAO,IAAI,CAAC;aACf;YAED,IAAM,KAAK,GAAG,uBAAU,CAAC,WAAW,EAAE,CAAC,eAAe,CAAC,KAAK,CAAC;YAC7D,IAAM,IAAI,GAAG,KAAK,CAAC,IAAI,CAAC,UAAC,CAAW,IAAK,OAAA,CAAC,CAAC,MAAM,KAAK,MAAM,EAAnB,CAAmB,CAAC,CAAC;YAE9D,IAAI,IAAI,EAAE;gBACN,OAAO,IAAI,CAAC;aACf;iBAAM;gBACH,OAAO,CAAC,IAAI,CAAC,oCAAS,MAAM,wBAAM,CAAC,CAAC;gBACpC,OAAO,IAAI,CAAC;aACf;SACJ;QAAC,OAAO,KAAK,EAAE;YACZ,OAAO,CAAC,KAAK,CAAC,6DAAc,KAAO,CAAC,CAAC;YACrC,OAAO,IAAI,CAAC;SACf;IACL,CAAC;IA10DD;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;qEACe;IAGnC;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;+DACS;IAG7B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;iEACW;IAG/B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,MAAM,CAAC;gEACU;IAG9B;QADC,QAAQ,CAAC,EAAE,CAAC,IAAI,CAAC;8DACQ;IApCT,uBAAuB;QAD3C,OAAO;OACa,uBAAuB,CA80D3C;IAAD,8BAAC;CA90DD,AA80DC,CA90DoD,EAAE,CAAC,SAAS,GA80DhE;kBA90DoB,uBAAuB", "file": "", "sourceRoot": "/", "sourcesContent": ["// Learn TypeScript:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html\n// Learn Attribute:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html\n// Learn life-cycle callbacks:\n//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html\n\nimport { RoomUser, HexCoord, PlayerActionDisplay } from \"../../bean/GameBean\";\nimport { GlobalBean } from \"../../bean/GlobalBean\";\nimport PlayerGameController from \"../../pfb/PlayerGameController \";\n\nconst {ccclass, property} = cc._decorator;\n\n// 六边形格子数据接口\nexport interface HexGridData {\n    q: number;  // 六边形坐标系q坐标\n    r: number;  // 六边形坐标系r坐标\n    worldPos: cc.Vec2;  // 格子在世界坐标系中的位置\n    hasPlayer: boolean;  // 是否已经放置了玩家预制体\n    playerNode?: cc.Node;  // 放置的玩家节点引用\n}\n\n@ccclass\nexport default class HexChessBoardController extends cc.Component {\n\n    @property(cc.Prefab)\n    playerGamePrefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    boomPrefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    biaojiPrefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    boom1Prefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    boom2Prefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    boom3Prefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    boom4Prefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    boom5Prefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    boom6Prefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    boom7Prefab: cc.Prefab = null;\n\n    @property(cc.Prefab)\n    boom8Prefab: cc.Prefab = null;\n\n    @property(cc.Node)\n    boardNode: cc.Node = null;  // 棋盘节点\n\n    // 六边形棋盘配置\n    private readonly HEX_SIZE = 44;  // 六边形半径\n    private readonly HEX_WIDTH = this.HEX_SIZE * 2;  // 六边形宽度\n    private readonly HEX_HEIGHT = this.HEX_SIZE * Math.sqrt(3);  // 六边形高度\n\n    // 格子数据存储 - 使用Map存储六边形坐标\n    private hexGridData: Map<string, HexGridData> = new Map();  // 存储六边形格子数据\n    private hexGridNodes: Map<string, cc.Node> = new Map();  // 存储六边形格子节点\n    private validHexCoords: HexCoord[] = [];  // 有效的六边形坐标列表\n\n    onLoad() {\n    \n    }\n\n    start() {\n        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成\n        this.scheduleOnce(() => {\n\n            this.setValidHexCoords([]);  // 传入空数组，但会被忽略\n\n            // 测试预制体位置计算\n            this.testHexPositionCalculation();\n\n            this.enableTouchForExistingGrids();\n        }, 0.1);\n    }\n\n    /**\n     * 设置有效的六边形坐标列表（忽略服务器数据，使用前端节点坐标）\n     * @param _coords 服务器发送的坐标列表（将被忽略）\n     */\n    public setValidHexCoords(_coords: HexCoord[]) {\n\n\n        // 忽略服务器传入的坐标，始终从节点名称自动生成\n        this.generateCoordsFromNodeNames();\n\n        this.initHexBoard();\n    }\n\n    /**\n     * 从节点名称自动生成有效坐标列表\n     */\n    private generateCoordsFromNodeNames() {\n        if (!this.boardNode) {\n            console.error(\"❌ 棋盘节点不存在，无法生成坐标列表\");\n            return;\n        }\n\n        const foundCoords: HexCoord[] = [];\n        const children = this.boardNode.children;\n\n       \n\n        for (let i = 0; i < children.length; i++) {\n            const child = children[i];\n            const coords = this.parseHexCoordinateFromName(child.name);\n\n            if (coords) {\n                // 检查是否已经存在相同的坐标\n                const exists = foundCoords.some(c => c.q === coords.q && c.r === coords.r);\n                if (!exists) {\n                    foundCoords.push({ q: coords.q, r: coords.r });\n                   \n                }\n            }\n        }\n\n        this.validHexCoords = foundCoords;\n       \n    }\n\n    // 初始化六边形棋盘\n    private initHexBoard() {\n        // 清空现有数据\n        this.hexGridData.clear();\n        this.hexGridNodes.clear();\n\n        // 初始化有效坐标的数据\n        for (const coord of this.validHexCoords) {\n            const key = this.getHexKey(coord.q, coord.r);\n            this.hexGridData.set(key, {\n                q: coord.q,\n                r: coord.r,\n                worldPos: this.getHexWorldPosition(coord.q, coord.r),\n                hasPlayer: false\n            });\n        }\n\n        this.createHexGridNodes();\n    }\n\n    // 生成六边形坐标的唯一键\n    private getHexKey(q: number, r: number): string {\n        return `${q},${r}`;\n    }\n\n    // 启用现有格子的触摸事件\n    private createHexGridNodes() {\n        if (!this.boardNode) {\n            console.error(\"棋盘节点未设置！\");\n            return;\n        }\n\n        // 如果格子已经存在，直接启用触摸事件\n        this.enableTouchForExistingGrids();\n    }\n\n    // 为现有格子启用触摸事件\n    private enableTouchForExistingGrids() {\n        // 检查棋盘节点是否存在\n        if (!this.boardNode) {\n            console.error(\"棋盘节点未设置，无法启用触摸事件！\");\n            return;\n        }\n\n        // 遍历棋盘节点的所有子节点\n        let children = this.boardNode.children;\n\n        for (let i = 0; i < children.length; i++) {\n            let child = children[i];\n\n            // 尝试从节点名称解析六边形坐标\n            let coords = this.parseHexCoordinateFromName(child.name);\n            if (coords) {\n                this.setupHexGridTouchEvents(child, coords.q, coords.r);\n                const key = this.getHexKey(coords.q, coords.r);\n                this.hexGridNodes.set(key, child);\n            } else {\n                // 如果无法从名称解析，尝试从位置计算\n                let pos = child.getPosition();\n                let coords = this.getHexCoordinateFromPosition(pos);\n                if (coords) {\n                    this.setupHexGridTouchEvents(child, coords.q, coords.r);\n                    const key = this.getHexKey(coords.q, coords.r);\n                    this.hexGridNodes.set(key, child);\n                }\n            }\n        }\n    }\n\n    // 从节点名称解析六边形坐标\n    private parseHexCoordinateFromName(nodeName: string): {q: number, r: number} | null {\n       \n\n        \n        const patterns = [\n            /^sixblock_(-?\\d+)_(-?\\d+)$/,   // sixblock_q_r 格式（您使用的格式）\n        ];\n\n        for (const pattern of patterns) {\n            const match = nodeName.match(pattern);\n            if (match) {\n                const coords = {q: parseInt(match[1]), r: parseInt(match[2])};\n              \n                return coords;\n            }\n        }\n\n        console.warn(`❌ 无法解析节点名称: ${nodeName}`);\n        return null;\n    }\n\n    // 从位置计算六边形坐标（近似）\n    private getHexCoordinateFromPosition(pos: cc.Vec2): {q: number, r: number} | null {\n        // 六边形坐标转换（从像素坐标到六边形坐标）\n        const x = pos.x;\n        const y = pos.y;\n        \n        // 使用六边形坐标转换公式\n        const q = Math.round((Math.sqrt(3)/3 * x - 1/3 * y) / this.HEX_SIZE);\n        const r = Math.round((2/3 * y) / this.HEX_SIZE);\n\n        // 检查是否为有效坐标\n        if (this.isValidHexCoordinate(q, r)) {\n            return {q: q, r: r};\n        }\n        return null;\n    }\n\n    // 计算六边形预制体的生成位置（直接使用您提供的格子中心坐标）\n    private getHexWorldPosition(q: number, r: number, isPlayerAvatar: boolean = false): cc.Vec2 {\n        // 您提供的精确格子中心坐标\n        const exactCoords = new Map<string, cc.Vec2>();\n        // 更新后的基准点坐标（与rowData保持一致）\n        exactCoords.set(\"0,0\", cc.v2(-300, -258));   // r=0行基准点\n        exactCoords.set(\"1,-1\", cc.v2(-258, -184));  // r=-1行基准点\n        exactCoords.set(\"1,-2\", cc.v2(-300, -108));  // r=-2行基准点\n        exactCoords.set(\"2,-3\", cc.v2(-258, -36));   // r=-3行基准点\n        exactCoords.set(\"2,-4\", cc.v2(-300, 37));    // r=-4行基准点\n        exactCoords.set(\"3,-5\", cc.v2(-258, 110));   // r=-5行基准点\n        exactCoords.set(\"3,-6\", cc.v2(-300, 185));   // r=-6行基准点\n        exactCoords.set(\"4,-7\", cc.v2(-258, 260));   // r=-7行基准点\n\n        // 首先检查是否有精确坐标\n        const key = `${q},${r}`;\n        if (exactCoords.has(key)) {\n            const pos = exactCoords.get(key);\n            // 如果是单人头像预制体，往左上偏移一点点\n            if (isPlayerAvatar) {\n                return cc.v2(pos.x , pos.y - 12); // 改为往左偏移10像素\n            }\n            return pos;\n        }\n\n        // 对于其他坐标，使用基于您提供的精确坐标数据进行计算\n\n        // 定义每一行的数据：使用统一步长86，保证美观整齐\n        const UNIFORM_STEP_X = 86; // 统一的x方向步长\n        const rowData = new Map<number, {baseQ: number, baseX: number, y: number}>();\n\n        // 基于您提供的更新数据，使用统一步长86\n        rowData.set(0, { baseQ: 0, baseX: -300, y: -258 });   // r=0行：基准点(0,0) → (-300, -258)\n        rowData.set(-1, { baseQ: 1, baseX: -258, y: -184 });  // r=-1行：基准点(1,-1) → (-258, -184)\n        rowData.set(-2, { baseQ: 1, baseX: -300, y: -108 });  // r=-2行：基准点(1,-2) → (-300, -108)\n        rowData.set(-3, { baseQ: 2, baseX: -258, y: -36 });   // r=-3行：基准点(2,-3) → (-258, -36)\n        rowData.set(-4, { baseQ: 2, baseX: -300, y: 37 });    // r=-4行：基准点(2,-4) → (-300, 37)\n        rowData.set(-5, { baseQ: 3, baseX: -258, y: 110 });   // r=-5行：基准点(3,-5) → (-258, 110)\n        rowData.set(-6, { baseQ: 3, baseX: -300, y: 185 });   // r=-6行：基准点(3,-6) → (-300, 185)\n        rowData.set(-7, { baseQ: 4, baseX: -258, y: 260 });   // r=-7行：基准点(4,-7) → (-258, 260)\n\n        // 计算基础位置\n        let x: number, y: number;\n\n        // 如果有该行的数据，使用统一步长计算\n        if (rowData.has(r)) {\n            const data = rowData.get(r);\n            x = data.baseX + (q - data.baseQ) * UNIFORM_STEP_X;\n            y = data.y;\n        } else {\n            // 对于其他行，使用通用的六边形轴线坐标系公式（也使用统一步长）\n            const baseX = -300;  // 更新为新的基准点\n            const baseY = -258;\n            const stepXR = -43;\n            const stepYR = 74;\n\n            x = baseX + q * UNIFORM_STEP_X + r * stepXR;\n            y = baseY - r * stepYR;\n        }\n\n        // 如果是单人头像预制体，往左上偏移一点点\n        if (isPlayerAvatar) {\n            y -= 12; // 往下偏移12像素（相比之前的-20，现在是-12，相当于往上调了8像素）\n        }\n\n        return cc.v2(x, y);\n    }\n\n    // 为六边形格子节点设置触摸事件\n    private setupHexGridTouchEvents(gridNode: cc.Node, q: number, r: number) {\n        // 安全检查：确保坐标有效\n        if (!this.isValidHexCoordinate(q, r)) {\n            console.error(`❌ setupHexGridTouchEvents: 尝试为无效坐标(${q},${r})设置触摸事件`);\n            return;\n        }\n\n        // 长按相关变量\n        let isLongPressing = false;\n        let longPressTimer = 0;\n        let longPressCallback: Function = null;\n        const LONG_PRESS_TIME = 1.0; // 1秒长按时间\n\n        // 触摸开始事件\n        gridNode.on(cc.Node.EventType.TOUCH_START, (_event: cc.Event.EventTouch) => {\n            isLongPressing = true;\n            longPressTimer = 0;\n\n            // 开始长按检测\n            longPressCallback = () => {\n                if (isLongPressing) {\n                    longPressTimer += 0.1;\n                    if (longPressTimer >= LONG_PRESS_TIME) {\n                        this.onHexGridLongPress(q, r);\n                        isLongPressing = false;\n                        if (longPressCallback) {\n                            this.unschedule(longPressCallback);\n                        }\n                    }\n                }\n            };\n            this.schedule(longPressCallback, 0.1);\n        }, this);\n\n        // 触摸结束事件\n        gridNode.on(cc.Node.EventType.TOUCH_END, (event: cc.Event.EventTouch) => {\n            // 如果不是长按，则执行点击事件\n            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {\n                this.onHexGridClick(q, r, event);\n            }\n\n            isLongPressing = false;\n            if (longPressCallback) {\n                this.unschedule(longPressCallback);\n            }\n        }, this);\n\n        // 触摸取消事件\n        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, (_event: cc.Event.EventTouch) => {\n            isLongPressing = false;\n            if (longPressCallback) {\n                this.unschedule(longPressCallback);\n            }\n        }, this);\n\n        // 添加Button组件以确保触摸响应\n        let button = gridNode.getComponent(cc.Button);\n        if (!button) {\n            button = gridNode.addComponent(cc.Button);\n            button.transition = cc.Button.Transition.SCALE;\n            button.zoomScale = 0.95;\n        }\n    }\n\n    // 六边形格子点击事件 - 发送挖掘操作\n    private onHexGridClick(q: number, r: number, _event?: cc.Event.EventTouch) {\n       \n\n        // 检查坐标是否有效\n        if (!this.isValidHexCoordinate(q, r)) {\n            console.warn(`❌ 无效的六边形坐标: (${q}, ${r})`);\n            return;\n        }\n\n        const key = this.getHexKey(q, r);\n        const gridData = this.hexGridData.get(key);\n\n        // 检查该位置是否已经有玩家预制体\n        if (gridData && gridData.hasPlayer) {\n            console.warn(`⚠️ 格子(${q}, ${r})已有玩家`);\n            return;\n        }\n\n       \n\n        // 发送挖掘操作事件 (action = 1)\n        this.node.emit('hex-chess-board-click', {\n            q: q,\n            r: r,\n            action: 1  // 1 = 挖掘\n        });\n    }\n\n    // 六边形格子长按事件 - 发送标记操作\n    private onHexGridLongPress(q: number, r: number) {\n    \n\n        // 检查坐标是否有效\n        if (!this.isValidHexCoordinate(q, r)) {\n            console.warn(`❌ 无效的六边形坐标: (${q}, ${r})`);\n            return;\n        }\n\n        const key = this.getHexKey(q, r);\n        const gridData = this.hexGridData.get(key);\n\n        // 检查该位置是否已经有玩家预制体\n        if (gridData && gridData.hasPlayer) {\n            console.warn(`⚠️ 格子(${q}, ${r})已有玩家`);\n            return;\n        }\n\n       \n\n        // 发送标记操作事件 (action = 2)\n        this.node.emit('hex-chess-board-click', {\n            q: q,\n            r: r,\n            action: 2  // 2 = 标记\n        });\n    }\n\n    // 检查六边形坐标是否有效\n    public isValidHexCoordinate(q: number, r: number): boolean {\n        const key = this.getHexKey(q, r);\n        return this.hexGridData.has(key);\n    }\n\n    // 在六边形格子上放置玩家预制体\n    public placePlayerOnHexGrid(q: number, r: number, withFlag: boolean = false) {\n        // 双重检查：确保坐标有效\n        if (!this.isValidHexCoordinate(q, r)) {\n            console.error(`❌ placePlayerOnHexGrid: 无效坐标(${q},${r})`);\n            return;\n        }\n\n        const key = this.getHexKey(q, r);\n        const gridData = this.hexGridData.get(key);\n        \n        // 双重检查：确保格子为空\n        if (!gridData || gridData.hasPlayer) {\n            console.error(`❌ placePlayerOnHexGrid: 格子(${q},${r})已有玩家，不能重复放置`);\n            return;\n        }\n\n        if (!this.playerGamePrefab) {\n            console.error(\"❌ 玩家预制体未设置！\");\n            return;\n        }\n\n        if (!this.boardNode) {\n            console.error(\"❌ 棋盘节点未设置！\");\n            return;\n        }\n\n        // 实例化玩家预制体\n        let playerNode = cc.instantiate(this.playerGamePrefab);\n\n        // 计算正确的位置（单人头像预制体，y轴+20）\n        let correctPosition = this.getHexWorldPosition(q, r, true);\n        playerNode.setPosition(correctPosition);\n\n        // 设置单人放置的缩放为0.8\n        playerNode.setScale(0.8);\n\n        // 先隐藏节点，等头像加载完成后再显示\n        playerNode.active = false;\n\n        // 处理Layout限制问题\n        this.addPlayerNodeSafely(playerNode);\n\n        // 设置头像和用户数据（异步加载）\n        this.setupPlayerAvatarAsync(playerNode, q, r, withFlag, () => {\n            // 头像加载完成的回调，播放生成动画\n            this.playAvatarSpawnAnimation(playerNode);\n        });\n\n        // 更新格子数据\n        gridData.hasPlayer = true;\n        gridData.playerNode = playerNode;\n    }\n\n    // 安全地添加玩家节点（处理Layout限制）\n    private addPlayerNodeSafely(playerNode: cc.Node) {\n        // 检查棋盘节点是否存在\n        if (!this.boardNode) {\n            console.error(\"棋盘节点未设置，无法添加玩家节点！\");\n            return;\n        }\n\n        // 检查棋盘节点是否有Layout组件\n        let layout = this.boardNode.getComponent(cc.Layout);\n        if (layout) {\n            // 临时禁用Layout\n            layout.enabled = false;\n            // 添加节点\n            this.boardNode.addChild(playerNode);\n        } else {\n            this.boardNode.addChild(playerNode);\n        }\n    }\n\n    // 异步设置玩家头像（带回调）\n    private setupPlayerAvatarAsync(playerNode: cc.Node, q: number, r: number, withFlag: boolean, onComplete: () => void) {\n        // 查找PlayerGameController组件（使用类引用）\n        let playerController = playerNode.getComponent(PlayerGameController);\n\n        if (playerController) {\n            // 检查avatar节点是否存在\n            if (playerController.avatar) {\n                // 检查avatar节点是否有Sprite组件\n                let avatarSprite = playerController.avatar.getComponent(cc.Sprite);\n                if (!avatarSprite) {\n                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);\n                }\n\n                // 确保avatar节点可见\n                playerController.avatar.active = true;\n                playerController.avatar.opacity = 255;\n            } else {\n                console.error(\"❌ PlayerGameController中的avatar节点为null\");\n                onComplete();\n                return;\n            }\n\n            // 设置旗子节点的显示状态\n            if (playerController.flagNode) {\n                playerController.flagNode.active = withFlag;\n\n                // 额外检查旗子节点的可见性\n                if (withFlag) {\n                    playerController.flagNode.opacity = 255;\n\n                    // 确保旗子节点的父节点也是可见的\n                    let parent = playerController.flagNode.parent;\n                    while (parent && parent !== playerNode) {\n                        parent.active = true;\n                        parent = parent.parent;\n                    }\n                }\n            } else {\n                console.warn(`⚠️ 找不到旗子节点 (${q},${r})`);\n            }\n\n            // 获取当前用户ID\n            const currentUserId = GlobalBean.GetInstance().loginData?.userInfo?.userId || `hex_player_${q}_${r}`;\n\n            // 在节点上存储userId，用于后续查找\n            playerNode['userId'] = currentUserId;\n\n            // 创建用户数据并设置头像\n            let userData = {\n                userId: currentUserId,\n                nickName: `玩家(${q},${r})`,\n                avatar: this.getDefaultAvatarUrl(),\n                score: 0,\n                pos: 0,\n                coin: 0,\n                status: 0,\n                rank: 0\n            } as RoomUser;\n\n            // 使用PlayerGameController的setData方法来设置头像\n            try {\n                playerController.setData(userData);\n\n                // 延迟设置旗子状态，确保在PlayerGameController初始化之后\n                this.scheduleOnce(() => {\n                    if (playerController.flagNode) {\n                        playerController.flagNode.active = withFlag;\n                    }\n                    onComplete();\n                }, 0.1);\n            } catch (error) {\n                console.error(\"设置头像数据失败:\", error);\n                onComplete();\n            }\n\n        } else {\n            console.warn(\"⚠️ 找不到PlayerGameController组件\");\n            onComplete();\n        }\n    }\n\n    // 获取默认头像URL\n    private getDefaultAvatarUrl(): string {\n        // 使用真实的头像URL\n        return \"https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg\";\n    }\n\n    /**\n     * 播放头像生成动画（由大变小，完全复制四边形棋盘控制器的逻辑）\n     * @param playerNode 玩家节点\n     */\n    private playAvatarSpawnAnimation(playerNode: cc.Node) {\n        if (!playerNode) {\n            console.warn(\"播放生成动画失败：节点为空\");\n            return;\n        }\n\n        // 显示节点\n        playerNode.active = true;\n\n        // 设置初始缩放为1.5倍（比正常大）\n        const originalScale = playerNode.scaleX;\n        const startScale = originalScale * 1.5;\n        playerNode.setScale(startScale);\n\n        // 使用cc.Tween创建由大变小的缩放动画\n        cc.tween(playerNode)\n            .to(0.3, { scaleX: originalScale, scaleY: originalScale }, { easing: 'backOut' })\n            .start();\n    }\n\n    // 清除指定六边形格子的玩家\n    public clearHexGridPlayer(q: number, r: number): boolean {\n        const key = this.getHexKey(q, r);\n        const gridData = this.hexGridData.get(key);\n\n        if (!gridData || !gridData.hasPlayer) {\n            return false;\n        }\n\n        // 移除玩家节点\n        if (gridData.playerNode) {\n            gridData.playerNode.removeFromParent();\n            gridData.playerNode = null;\n        }\n\n        // 更新数据\n        gridData.hasPlayer = false;\n\n        return true;\n    }\n\n    // 清除所有玩家\n    public clearAllPlayers() {\n        let clearedCount = 0;\n\n        // 1. 清理存储在hexGridData中的玩家节点（自己的头像）\n        this.hexGridData.forEach((gridData) => {\n            if (gridData.hasPlayer && gridData.playerNode) {\n                gridData.playerNode.removeFromParent();\n                gridData.playerNode = null;\n                gridData.hasPlayer = false;\n                clearedCount++;\n            }\n        });\n\n        // 2. 清理棋盘上的其他玩家头像节点\n        if (this.boardNode) {\n            const children = this.boardNode.children.slice(); // 创建副本避免遍历时修改数组\n            for (let i = 0; i < children.length; i++) {\n                const child = children[i];\n                if (child.name === \"player_game_pfb\") {\n                    // 检查是否有PlayerGameController组件\n                    const playerController = child.getComponent(PlayerGameController);\n                    if (playerController) {\n                        child.removeFromParent();\n                        clearedCount++;\n                    }\n                }\n            }\n        }\n\n    }\n\n    /**\n     * 清理所有玩家预制体（新回合开始时调用）\n     * 包括自己的头像和其他玩家的头像\n     * 为了与四边形棋盘控制器保持一致的接口\n     */\n    public clearAllPlayerNodes() {\n        this.clearAllPlayers();\n    }\n\n    // 获取所有已放置玩家的六边形坐标\n    public getAllPlayerHexCoordinates(): {q: number, r: number}[] {\n        let coordinates: {q: number, r: number}[] = [];\n\n        this.hexGridData.forEach((gridData) => {\n            if (gridData.hasPlayer) {\n                coordinates.push({q: gridData.q, r: gridData.r});\n            }\n        });\n\n        return coordinates;\n    }\n\n    // 检查六边形格子是否为空\n    public isHexGridEmpty(q: number, r: number): boolean {\n        if (!this.isValidHexCoordinate(q, r)) {\n            return false;\n        }\n\n        const key = this.getHexKey(q, r);\n        const gridData = this.hexGridData.get(key);\n        return gridData ? !gridData.hasPlayer : false;\n    }\n\n    /**\n     * 重置游戏场景（游戏开始时调用）\n     * 清除数字、炸弹、标记预制体，重新显示所有小格子\n     */\n    public resetGameScene() {\n        if (!this.boardNode) {\n            console.error(\"❌ 棋盘节点不存在，无法重置\");\n            return;\n        }\n\n        // 清除所有游戏元素（数字、炸弹、标记等）\n        this.clearAllGameElements();\n\n        // 显示所有小格子\n        this.showAllHexGrids();\n\n        // 重新初始化棋盘数据\n        this.reinitializeHexBoardData();\n    }\n\n    /**\n     * 清除所有游戏元素（数字、炸弹、标记、玩家头像等），但保留小格子\n     */\n    private clearAllGameElements() {\n        if (!this.boardNode) {\n            return;\n        }\n\n        const childrenToRemove: cc.Node[] = [];\n\n        // 遍历棋盘的所有子节点\n        for (let i = 0; i < this.boardNode.children.length; i++) {\n            const child = this.boardNode.children[i];\n            const nodeName = child.name;\n\n            // 检查是否是需要清除的游戏元素（不包括小格子）\n            if (this.isGameElement(child, nodeName)) {\n                childrenToRemove.push(child);\n            }\n        }\n\n        // 移除找到的游戏元素\n        childrenToRemove.forEach((child) => {\n            child.removeFromParent();\n        });\n    }\n\n    /**\n     * 判断节点是否是游戏元素（需要清除的），小格子不会被清除\n     */\n    private isGameElement(node: cc.Node, nodeName: string): boolean {\n        // 绝对不清除的节点（六边形小格子）\n        if (nodeName.startsWith(\"HexGrid_\") || nodeName === \"hexblock\") {\n            return false;\n        }\n\n        // 分数控制器不清除\n        if (nodeName.includes(\"Score\") || nodeName.includes(\"score\")) {\n            return false;\n        }\n\n        // UI相关节点不清除\n        if (nodeName.includes(\"UI\") || nodeName.includes(\"ui\")) {\n            return false;\n        }\n\n        // 明确需要清除的游戏预制体\n        // 炸弹预制体\n        if (nodeName === \"Boom\") {\n            return true;\n        }\n\n        // 数字预制体（Boom1, Boom2, Boom3 等）\n        if (nodeName.match(/^Boom\\d+$/)) {\n            return true;\n        }\n\n        // 临时数字节点（NeighborMines_1, NeighborMines_2 等）\n        if (nodeName.match(/^NeighborMines_\\d+$/)) {\n            return true;\n        }\n\n        // 测试节点（Test_q_r 格式）\n        if (nodeName.match(/^Test_-?\\d+_-?\\d+$/)) {\n            return true;\n        }\n\n        // 玩家预制体（通过组件判断）\n        if (node.getComponent(PlayerGameController)) {\n            return true;\n        }\n\n        // 标记预制体\n        if (nodeName.includes(\"Flag\") || nodeName.includes(\"Mark\") || nodeName.includes(\"flag\") ||\n            nodeName === \"Biaoji\" || nodeName.includes(\"Biaoji\")) {\n            return true;\n        }\n\n        // 玩家头像预制体\n        if (nodeName.includes(\"Player\") || nodeName.includes(\"Avatar\")) {\n            return true;\n        }\n\n        // 默认保留未知节点（保守策略）\n        return false;\n    }\n\n    /**\n     * 显示所有六边形小格子（第二把游戏开始时恢复被隐藏的小格子）\n     */\n    private showAllHexGrids() {\n        if (!this.boardNode) {\n            return;\n        }\n\n        // 遍历棋盘的所有子节点，找到小格子并显示\n        for (let i = 0; i < this.boardNode.children.length; i++) {\n            const child = this.boardNode.children[i];\n\n            // 如果是六边形小格子节点\n            if (child.name.startsWith(\"HexGrid_\") || child.name === \"hexblock\") {\n                // 停止所有可能正在进行的动画\n                child.stopAllActions();\n\n                // 恢复显示状态\n                child.active = true;\n                child.opacity = 255;\n                child.scaleX = 1;\n                child.scaleY = 1;\n\n                // 确保格子可以交互\n                const button = child.getComponent(cc.Button);\n                if (button) {\n                    button.enabled = true;\n                }\n            }\n        }\n    }\n\n    /**\n     * 隐藏指定位置的六边形小格子（点击时调用）\n     * @param q 六边形q坐标\n     * @param r 六边形r坐标\n     * @param immediate 是否立即隐藏（不播放动画）\n     */\n    public hideHexGridAt(q: number, r: number, immediate: boolean = false) {\n        if (!this.isValidHexCoordinate(q, r)) {\n            console.warn(`隐藏格子失败：坐标(${q}, ${r})无效`);\n            return;\n        }\n\n        // 获取格子节点\n        const key = this.getHexKey(q, r);\n        const gridNode = this.hexGridNodes.get(key);\n        if (gridNode) {\n            if (immediate) {\n                // 立即隐藏，不播放动画\n                gridNode.active = false;\n            } else {\n                // 使用动画隐藏格子\n                cc.tween(gridNode)\n                    .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })\n                    .call(() => {\n                        gridNode.active = false;\n                    })\n                    .start();\n            }\n        }\n    }\n\n    /**\n     * 重新初始化六边形棋盘数据\n     */\n    private reinitializeHexBoardData() {\n        // 重置hexGridData中的玩家状态\n        this.hexGridData.forEach((gridData) => {\n            gridData.hasPlayer = false;\n            gridData.playerNode = null;\n        });\n    }\n\n    /**\n     * 获取六边形格子数据\n     */\n    public getHexGridData(q: number, r: number): HexGridData | null {\n        const key = this.getHexKey(q, r);\n        return this.hexGridData.get(key) || null;\n    }\n\n    /**\n     * 批量放置玩家（用于从服务器同步数据）\n     */\n    public batchPlaceHexPlayers(coordinates: {q: number, r: number}[]) {\n        coordinates.forEach(coord => {\n            if (this.isValidHexCoordinate(coord.q, coord.r) && this.isHexGridEmpty(coord.q, coord.r)) {\n                this.placePlayerOnHexGrid(coord.q, coord.r);\n            }\n        });\n    }\n\n    /**\n     * 测试点击功能（调试用）\n     */\n    public testHexClick(q: number, r: number) {\n        this.onHexGridClick(q, r);\n    }\n\n    /**\n     * 获取棋盘状态信息（调试用）\n     */\n    public getHexBoardInfo() {\n        let info = {\n            validHexCoordsCount: this.validHexCoords.length,\n            boardNodeChildren: this.boardNode ? this.boardNode.children.length : 0,\n            playerCount: this.getAllPlayerHexCoordinates().length,\n            hasPlayerGamePrefab: !!this.playerGamePrefab,\n            hasBoardNode: !!this.boardNode,\n            hexGridDataSize: this.hexGridData.size,\n            hexGridNodesSize: this.hexGridNodes.size\n        };\n\n        return info;\n    }\n\n    /**\n     * 获取前端节点的总数量（用于计算炸弹数量）\n     */\n    public getHexGridCount(): number {\n        return this.validHexCoords.length;\n    }\n\n    /**\n     * 根据前端节点数量计算推荐的炸弹数量\n     */\n    public getRecommendedMineCount(): number {\n        const gridCount = this.getHexGridCount();\n        if (gridCount === 0) {\n            return 13; // 默认值\n        }\n\n        // 约15%的格子是炸弹\n        const mineCount = Math.floor(gridCount * 0.15);\n\n        return Math.max(mineCount, 5); // 至少5个炸弹\n    }\n\n    /**\n     * 测试六边形预制体位置计算是否正确\n     */\n    public testHexPositionCalculation() {\n       \n\n        // 测试更新后的基准点坐标\n        const testPoints = [\n            { q: 0, r: 0, expected: { x: -300, y: -258 }, desc: \"r=0行基准点(0,0)\" },\n            { q: 1, r: -1, expected: { x: -258, y: -184 }, desc: \"r=-1行基准点(1,-1)\" },\n            { q: 1, r: -2, expected: { x: -300, y: -108 }, desc: \"r=-2行基准点(1,-2)\" },\n            { q: 2, r: -3, expected: { x: -258, y: -36 }, desc: \"r=-3行基准点(2,-3)\" },\n            { q: 2, r: -4, expected: { x: -300, y: 37 }, desc: \"r=-4行基准点(2,-4)\" },\n            { q: 3, r: -5, expected: { x: -258, y: 110 }, desc: \"r=-5行基准点(3,-5)\" },\n            { q: 3, r: -6, expected: { x: -300, y: 185 }, desc: \"r=-6行基准点(3,-6)\" },\n            { q: 4, r: -7, expected: { x: -258, y: 260 }, desc: \"r=-7行基准点(4,-7)\" }\n        ];\n\n        let correctCount = 0;\n\n        testPoints.forEach(point => {\n            const calculated = this.getHexWorldPosition(point.q, point.r);\n            const errorX = Math.abs(calculated.x - point.expected.x);\n            const errorY = Math.abs(calculated.y - point.expected.y);\n            const isCorrect = errorX < 2 && errorY < 2; // 允许2像素误差\n\n            if (isCorrect) correctCount++;\n\n            \n        });\n\n       \n        // 测试一些中间坐标\n\n        const intermediatePoints = [\n            // r=0行测试\n            { q: 2, r: 0 }, { q: 3, r: 0 }, { q: 4, r: 0 }, { q: 5, r: 0 }, { q: 6, r: 0 },\n            // r=-1行测试\n            { q: 3, r: -1 }, { q: 4, r: -1 }, { q: 5, r: -1 }, { q: 6, r: -1 },\n            // r=-2行测试\n            { q: 3, r: -2 }, { q: 4, r: -2 }, { q: 5, r: -2 }, { q: 6, r: -2 }, { q: 7, r: -2 },\n            // r=-3行测试\n            { q: 4, r: -3 }, { q: 5, r: -3 }, { q: 6, r: -3 }, { q: 7, r: -3 }\n        ];\n\n        intermediatePoints.forEach(point => {\n            const calculated = this.getHexWorldPosition(point.q, point.r);\n           \n        });\n\n        // 暴露到全局以便调试\n        (window as any).testHexPositions = () => this.testHexPositionCalculation();\n    }\n\n    // ==================== NoticeActionDisplay 相关方法 ====================\n    // 以下方法与第一张地图（方形地图）的逻辑完全一样，用于处理加分和掀开地图\n\n    /**\n     * 在指定六边形位置创建boom预制体\n     * @param q 六边形q坐标\n     * @param r 六边形r坐标\n     * @param isCurrentUser 是否是当前用户点到的雷\n     */\n    public createHexBoomPrefab(q: number, r: number, isCurrentUser: boolean = true) {\n        if (!this.boomPrefab) {\n            console.error(\"boomPrefab 预制体未设置，请在编辑器中挂载\");\n            return;\n        }\n\n        // 实例化boom预制体\n        const boomNode = cc.instantiate(this.boomPrefab);\n        boomNode.name = \"HexBoom\";\n\n        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）\n        const position = this.getHexWorldPosition(q, r, false);\n        boomNode.setPosition(position);\n\n        // 添加到棋盘\n        this.boardNode.addChild(boomNode);\n\n        // 播放出现动画\n        boomNode.setScale(0);\n        cc.tween(boomNode)\n            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })\n            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })\n            .start();\n\n        // 只有当前用户点到雷时才播放棋盘震动效果\n        if (isCurrentUser) {\n            this.playBoardShakeAnimation();\n        }\n    }\n\n    /**\n     * 在指定六边形位置创建biaoji预制体\n     * @param q 六边形q坐标\n     * @param r 六边形r坐标\n     */\n    public createHexBiaojiPrefab(q: number, r: number) {\n        if (!this.biaojiPrefab) {\n            console.error(\"biaojiPrefab 预制体未设置，请在编辑器中挂载\");\n            return;\n        }\n\n        // 实例化biaoji预制体\n        const biaojiNode = cc.instantiate(this.biaojiPrefab);\n        biaojiNode.name = \"HexBiaoji\";\n\n        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）\n        const position = this.getHexWorldPosition(q, r, false);\n        biaojiNode.setPosition(position);\n\n        // 添加到棋盘\n        this.boardNode.addChild(biaojiNode);\n\n        // 播放出现动画\n        biaojiNode.setScale(0);\n        cc.tween(biaojiNode)\n            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })\n            .start();\n    }\n\n    /**\n     * 更新指定六边形位置的neighborMines显示\n     * @param q 六边形q坐标\n     * @param r 六边形r坐标\n     * @param neighborMines 周围地雷数量\n     */\n    public updateHexNeighborMinesDisplay(q: number, r: number, neighborMines: number) {\n        // 0不需要显示数字\n        if (neighborMines === 0) {\n            return;\n        }\n\n        // 直接使用boom数字预制体\n        this.createHexNumberPrefab(q, r, neighborMines);\n    }\n\n    /**\n     * 创建六边形数字预制体（boom1, boom2, ...）\n     * @param q 六边形q坐标\n     * @param r 六边形r坐标\n     * @param number 数字\n     */\n    private createHexNumberPrefab(q: number, r: number, number: number) {\n        // 根据数字选择对应的预制体\n        let prefab: cc.Prefab = null;\n        switch (number) {\n            case 1: prefab = this.boom1Prefab; break;\n            case 2: prefab = this.boom2Prefab; break;\n            case 3: prefab = this.boom3Prefab; break;\n            case 4: prefab = this.boom4Prefab; break;\n            case 5: prefab = this.boom5Prefab; break;\n            case 6: prefab = this.boom6Prefab; break;\n            case 7: prefab = this.boom7Prefab; break;\n            case 8: prefab = this.boom8Prefab; break;\n            default:\n                console.error(`不支持的数字: ${number}`);\n                return;\n        }\n\n        if (!prefab) {\n            console.error(`boom${number}Prefab 预制体未设置，请在编辑器中挂载`);\n            return;\n        }\n\n        // 实例化数字预制体\n        const numberNode = cc.instantiate(prefab);\n        numberNode.name = `HexBoom${number}`;\n\n        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）\n        const position = this.getHexWorldPosition(q, r, false);\n        numberNode.setPosition(position);\n\n        // 添加到棋盘\n        this.boardNode.addChild(numberNode);\n\n        // 播放出现动画\n        numberNode.setScale(0);\n        cc.tween(numberNode)\n            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })\n            .start();\n    }\n\n    /**\n     * 播放棋盘震动动画（当前用户点到雷时）\n     */\n    private playBoardShakeAnimation() {\n        if (!this.boardNode) {\n            return;\n        }\n\n        const originalPosition = this.boardNode.getPosition();\n        const shakeDistance = 10;\n\n        cc.tween(this.boardNode)\n            .to(0.05, { x: originalPosition.x + shakeDistance })\n            .to(0.05, { x: originalPosition.x - shakeDistance })\n            .to(0.05, { x: originalPosition.x + shakeDistance })\n            .to(0.05, { x: originalPosition.x - shakeDistance })\n            .to(0.05, { x: originalPosition.x })\n            .start();\n    }\n\n    // ==================== 头像生命周期管理 ====================\n    // 以下方法与第一张地图的逻辑完全一样，用于管理头像预制体的生命周期\n\n    /**\n     * 让所有六边形头像消失（和第一张地图的hideAvatarsAtPosition逻辑一样）\n     * @param onComplete 完成回调\n     */\n    public hideAllHexAvatars(onComplete: () => void) {\n        if (!this.boardNode) {\n            console.warn(\"棋盘节点不存在，无法清理六边形头像\");\n            onComplete();\n            return;\n        }\n\n        // 收集所有头像节点（参考第一张地图的逻辑）\n        const avatarNodes: cc.Node[] = [];\n\n        // 方法1: 收集存储在hexGridData中的玩家节点（自己的头像）\n        this.hexGridData.forEach((gridData) => {\n            if (gridData.hasPlayer && gridData.playerNode) {\n                avatarNodes.push(gridData.playerNode);\n            }\n        });\n\n        // 方法2: 收集棋盘上所有的玩家预制体节点（包括其他玩家的头像）\n        for (let i = 0; i < this.boardNode.children.length; i++) {\n            const child = this.boardNode.children[i];\n\n            // 检查是否是玩家预制体（通过组件判断）\n            const playerController = child.getComponent(PlayerGameController);\n            if (playerController) {\n                // 避免重复添加（可能已经在方法1中添加过）\n                if (!avatarNodes.includes(child)) {\n                    avatarNodes.push(child);\n                }\n            }\n        }\n\n       \n\n        // 如果没有头像，直接执行回调\n        if (avatarNodes.length === 0) {\n            this.clearAllMyHexAvatarReferences();\n            onComplete();\n            return;\n        }\n\n        let completedCount = 0;\n        const totalCount = avatarNodes.length;\n\n        // 为每个头像播放消失动画（和第一张地图完全一样）\n        avatarNodes.forEach((avatarNode) => {\n            // 使用cc.Tween播放消失动画\n            cc.tween(avatarNode)\n                .to(0.3, { opacity: 0, scaleX: 0.5, scaleY: 0.5 }, { easing: 'sineIn' })\n                .call(() => {\n                    // 动画完成后移除节点\n                    avatarNode.removeFromParent();\n                    completedCount++;\n\n                    // 所有头像都消失完成后，执行回调\n                    if (completedCount >= totalCount) {\n                        // 清除所有自己头像的引用\n                        this.clearAllMyHexAvatarReferences();\n                        onComplete();\n                    }\n                })\n                .start();\n        });\n    }\n\n    /**\n     * 清除所有自己六边形头像的引用（和第一张地图的clearAllMyAvatarReferences逻辑一样）\n     */\n    private clearAllMyHexAvatarReferences() {\n        this.hexGridData.forEach((gridData) => {\n            if (gridData.hasPlayer) {\n                gridData.hasPlayer = false;\n                gridData.playerNode = null;\n            }\n        });\n        \n    }\n\n    // ==================== 加分逻辑相关方法 ====================\n    // 以下方法与第一张地图的加分逻辑完全一样\n\n    /**\n     * 在指定六边形位置的玩家节点上显示分数\n     * @param q 六边形q坐标\n     * @param r 六边形r坐标\n     * @param score 分数\n     * @param showPlusOne 是否显示+1（先手奖励）\n     */\n    public showScoreOnHexPlayerNode(q: number, r: number, score: number, showPlusOne: boolean) {\n        // 查找该位置的玩家节点\n        const playerNode = this.findHexPlayerNodeAtPosition(q, r);\n        if (!playerNode) {\n            // 在NoticeActionDisplay流程中，头像会被清理，找不到节点是正常的\n            \n            return;\n        }\n\n        // 获取PlayerGameController组件\n        const playerController = playerNode.getComponent(PlayerGameController);\n        if (!playerController) {\n            console.warn(\"找不到PlayerGameController组件\");\n            return;\n        }\n\n        // 显示分数动画\n        if (showPlusOne) {\n            // 先显示+1，再显示本回合得分\n            this.showScoreAnimationOnHexNode(playerController, 1, () => {\n                this.scheduleOnce(() => {\n                    this.showScoreAnimationOnHexNode(playerController, score, null);\n                }, 1.0);\n            });\n        } else {\n            // 只显示本回合得分\n            this.showScoreAnimationOnHexNode(playerController, score, null);\n        }\n    }\n\n    /**\n     * 查找指定六边形位置的玩家节点\n     * @param q 六边形q坐标\n     * @param r 六边形r坐标\n     * @returns 玩家节点或null\n     */\n    private findHexPlayerNodeAtPosition(q: number, r: number): cc.Node | null {\n        // 方法1: 从hexGridData中查找（自己的头像）\n        const key = this.getHexKey(q, r);\n        const gridData = this.hexGridData.get(key);\n        if (gridData && gridData.hasPlayer && gridData.playerNode) {\n            return gridData.playerNode;\n        }\n\n        // 方法2: 在棋盘上查找其他玩家的头像\n        if (!this.boardNode) {\n            return null;\n        }\n\n        // 遍历棋盘上的所有子节点，查找player_game_pfb\n        const children = this.boardNode.children;\n        for (let i = 0; i < children.length; i++) {\n            const child = children[i];\n            if (child.name === \"player_game_pfb\") {\n                // 检查位置是否匹配（允许一定的误差）\n                const expectedPos = this.getHexWorldPosition(q, r, true);\n                const actualPos = child.getPosition();\n                const distance = expectedPos.sub(actualPos).mag();\n                if (distance < 10) { // 10像素误差范围内\n                    return child;\n                }\n            }\n        }\n\n        return null;\n    }\n\n    /**\n     * 在六边形节点上显示分数动画\n     * @param playerController 玩家控制器\n     * @param score 分数\n     * @param onComplete 完成回调\n     */\n    private showScoreAnimationOnHexNode(playerController: any, score: number, onComplete: (() => void) | null) {\n        // 调用PlayerGameController的showAddScore方法\n        if (playerController && typeof playerController.showAddScore === 'function') {\n            playerController.showAddScore(score);\n        }\n\n        if (onComplete) {\n            this.scheduleOnce(onComplete, 1.0);\n        }\n    }\n\n    /**\n     * 显示玩家游戏加减分效果（完全复制四边形棋盘控制器的逻辑）\n     * @param userId 用户ID\n     * @param score 分数变化（正数为加分，负数为减分）\n     */\n    public showHexPlayerGameScore(userId: string, score: number) {\n        const currentUserId = this.getCurrentHexUserId();\n        let foundPlayer = false;\n\n        // 1. 如果是当前用户，查找自己的玩家节点（存储在hexGridData中）\n        if (userId === currentUserId) {\n            foundPlayer = this.showScoreForCurrentHexUser(score);\n        } else {\n            // 2. 如果是其他用户，查找对应的玩家头像节点\n            foundPlayer = this.showScoreForOtherHexUser(userId, score);\n        }\n\n        if (!foundPlayer) {\n            console.warn(`未找到用户 ${userId} 的六边形头像节点来显示分数效果`);\n        }\n    }\n\n    /**\n     * 获取当前用户ID（复制四边形棋盘控制器的方法）\n     */\n    private getCurrentHexUserId(): string {\n        return GlobalBean.GetInstance().loginData?.userInfo?.userId || \"\";\n    }\n\n    /**\n     * 为当前用户显示分数效果（复制四边形棋盘控制器的逻辑）\n     */\n    private showScoreForCurrentHexUser(score: number): boolean {\n        let foundPlayer = false;\n\n        this.hexGridData.forEach((gridData) => {\n            // 如果已经找到了，就不再继续查找\n            if (foundPlayer) {\n                return;\n            }\n\n            if (gridData.hasPlayer && gridData.playerNode) {\n                let playerController = gridData.playerNode.getComponent(\"PlayerGameController\") ||\n                                     gridData.playerNode.getComponent(\"PlayerGameController \");\n\n                if (playerController) {\n                    \n                    this.showScoreOnHexPlayerController(playerController, score);\n                    foundPlayer = true;\n                }\n            }\n        });\n\n        if (!foundPlayer) {\n            console.warn(\"❌ 未找到当前用户的六边形头像节点\");\n        }\n\n        return foundPlayer;\n    }\n\n    /**\n     * 为其他用户显示分数效果（复制四边形棋盘控制器的逻辑）\n     */\n    private showScoreForOtherHexUser(userId: string, score: number): boolean {\n        if (!this.boardNode) {\n            return false;\n        }\n\n        // 遍历棋盘上的所有玩家头像节点\n        return this.findHexPlayerNodeByUserId(userId, score);\n    }\n\n    /**\n     * 根据userId查找对应的玩家节点（复制四边形棋盘控制器的逻辑）\n     */\n    private findHexPlayerNodeByUserId(userId: string, score: number): boolean {\n        if (!this.boardNode) {\n            console.warn(`棋盘节点不存在，无法查找用户 ${userId} 的头像`);\n            return false;\n        }\n\n        // 遍历棋盘上的所有玩家头像节点，根据存储的userId精确匹配\n        for (let i = 0; i < this.boardNode.children.length; i++) {\n            const child = this.boardNode.children[i];\n\n            // 尝试多种方式获取PlayerGameController组件\n            let playerController = child.getComponent(\"PlayerGameController\");\n            if (!playerController) {\n                playerController = child.getComponent(\"PlayerGameController \");  // 注意末尾有空格\n            }\n            if (!playerController) {\n                // 尝试通过类名获取\n                const components = child.getComponents(cc.Component);\n                playerController = components.find(comp =>\n                    comp.constructor.name === 'PlayerGameController' ||\n                    comp.constructor.name === 'PlayerGameController '\n                );\n            }\n\n            const storedUserId = child['userId'];\n\n            if (storedUserId === userId) {\n                if (playerController) {\n                    // 找到匹配的用户ID和组件，显示分数效果\n                    this.showScoreOnHexPlayerController(playerController, score);\n                    return true;\n                } else {\n                    // 找到匹配的用户ID但没有组件\n                    console.warn(`⚠️ 找到用户 ${userId} 的节点但没有PlayerGameController组件`);\n                    return false;  // 找到节点但没有组件，返回false\n                }\n            }\n        }\n\n        console.warn(`❌ 未找到用户 ${userId} 的六边形头像节点`);\n        return false;\n    }\n\n    /**\n     * 在PlayerController上显示分数效果（复制四边形棋盘控制器的逻辑）\n     */\n    private showScoreOnHexPlayerController(playerController: any, score: number) {\n        // 临时提升节点层级，避免被其他头像遮挡\n        const playerNode = playerController.node;\n        const originalSiblingIndex = playerNode.getSiblingIndex();\n\n        // 将节点移到最上层\n        playerNode.setSiblingIndex(-1);\n\n        // 同时确保加分/减分节点的层级更高\n        this.ensureHexScoreNodeTopLevel(playerController);\n\n        if (score > 0) {\n            playerController.showAddScore(score);\n        } else if (score < 0) {\n            playerController.showSubScore(Math.abs(score));\n        }\n\n        // 延迟恢复原始层级（等分数动画播放完成）\n        this.scheduleOnce(() => {\n            if (playerNode && playerNode.isValid) {\n                playerNode.setSiblingIndex(originalSiblingIndex);\n            }\n        }, 2.5); // 增加到2.5秒，确保动画完全结束\n    }\n\n    /**\n     * 确保六边形加分/减分节点在最高层级\n     */\n    private ensureHexScoreNodeTopLevel(playerController: any) {\n        // 设置加分节点的最高层级\n        if (playerController.addScoreNode) {\n            playerController.addScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;\n        }\n\n        // 设置减分节点的最高层级\n        if (playerController.subScoreNode) {\n            playerController.subScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;\n        }\n\n       \n    }\n\n   \n\n    /**\n     * 查找指定用户ID的所有六边形头像节点\n     * @param userId 用户ID\n     * @returns 头像节点数组\n     */\n    private findAllHexPlayerNodesByUserId(userId: string): cc.Node[] {\n        const playerNodes: cc.Node[] = [];\n\n        if (!this.boardNode) {\n            return playerNodes;\n        }\n\n        // 遍历棋盘上的所有子节点\n        const children = this.boardNode.children;\n        for (let i = 0; i < children.length; i++) {\n            const child = children[i];\n\n            // 检查是否是玩家预制体（通过组件判断）\n            const playerController = child.getComponent(PlayerGameController);\n            if (playerController) {\n                // 检查是否是指定用户的头像（使用存储在节点上的userId）\n                const storedUserId = child['userId'];\n                if (storedUserId === userId) {\n                    playerNodes.push(child);\n                }\n            }\n        }\n\n        // 也检查存储在hexGridData中的玩家节点\n        this.hexGridData.forEach((gridData) => {\n            if (gridData.hasPlayer && gridData.playerNode) {\n                const playerController = gridData.playerNode.getComponent(PlayerGameController);\n                const storedUserId = gridData.playerNode['userId'];\n                if (playerController && storedUserId === userId) {\n                    // 避免重复添加\n                    if (!playerNodes.includes(gridData.playerNode)) {\n                        playerNodes.push(gridData.playerNode);\n                    }\n                }\n            }\n        });\n\n        return playerNodes;\n    }\n\n    // ==================== 其他玩家头像生成 ====================\n    // 以下方法与第一张地图的逻辑完全一样，用于生成其他玩家的头像\n\n    /**\n     * 在指定六边形位置显示其他玩家的操作（完全复制四边形棋盘控制器的逻辑）\n     * @param q 六边形q坐标\n     * @param r 六边形r坐标\n     * @param actions 该位置的其他玩家操作列表\n     */\n    public displayOtherPlayersAtHexPosition(q: number, r: number, actions: PlayerActionDisplay[]) {\n        if (!this.isValidHexCoordinate(q, r) || !actions || actions.length === 0) {\n            console.warn(`无效参数: (${q}, ${r}), actions: ${actions?.length || 0}`);\n            return;\n        }\n\n        // 检查该位置是否已经有自己的头像\n        const key = this.getHexKey(q, r);\n        const gridData = this.hexGridData.get(key);\n\n        if (gridData && gridData.hasPlayer) {\n            // 只有当真的有其他玩家时，才调整自己的头像位置\n            if (actions.length > 0) {\n                // 如果已有自己的头像且有其他玩家，需要使用多人布局策略\n                this.addOtherPlayersToExistingHexGrid(q, r, actions);\n            }\n        } else {\n            // 如果没有自己的头像，直接添加其他玩家头像\n            this.addOtherPlayersToEmptyHexGrid(q, r, actions);\n        }\n    }\n\n    /**\n     * 在已有自己头像的六边形格子上添加其他玩家头像，并调整自己的头像位置和缩放\n     * （完全复制四边形棋盘控制器的逻辑）\n     * @param q 六边形q坐标\n     * @param r 六边形r坐标\n     * @param actions 其他玩家操作列表\n     */\n    private addOtherPlayersToExistingHexGrid(q: number, r: number, actions: PlayerActionDisplay[]) {\n        // 总玩家数 = 自己(1) + 其他玩家数量\n        const totalPlayers = 1 + actions.length;\n        const positions = this.getHexPlayerPositions(totalPlayers);\n\n        // 第一步：调整自己的头像位置和缩放\n        const myPosition = positions[0]; // 第一个位置是自己的\n        this.adjustMyHexAvatarPosition(q, r, myPosition, actions);\n\n        // 第二步：从第二个位置开始放置其他玩家\n        for (let i = 0; i < actions.length; i++) {\n            const action = actions[i];\n            const position = positions[i + 1]; // 跳过第一个位置（自己的位置）\n\n            // 使用六边形坐标系创建其他玩家头像\n            this.createOtherPlayerAtHexPosition(q, r, action, position, totalPlayers);\n        }\n    }\n\n    /**\n     * 在空六边形格子上添加其他玩家头像\n     * （完全复制四边形棋盘控制器的逻辑）\n     * @param q 六边形q坐标\n     * @param r 六边形r坐标\n     * @param actions 其他玩家操作列表\n     */\n    private addOtherPlayersToEmptyHexGrid(q: number, r: number, actions: PlayerActionDisplay[]) {\n        const totalPlayers = actions.length; // 空格子上只有其他玩家\n        const positions = this.getHexPlayerPositions(totalPlayers);\n\n        for (let i = 0; i < actions.length; i++) {\n            const action = actions[i];\n            const position = positions[i];\n\n            // 使用六边形坐标系创建其他玩家头像\n            this.createOtherPlayerAtHexPosition(q, r, action, position, totalPlayers);\n        }\n    }\n\n\n\n    /**\n     * 根据玩家数量获取六边形布局位置（完全复制四边形棋盘控制器的逻辑）\n     * @param playerCount 玩家数量\n     * @returns 位置数组 {x: number, y: number, scale: number}[]\n     */\n    private getHexPlayerPositions(playerCount: number): {x: number, y: number, scale: number}[] {\n        switch (playerCount) {\n            case 1:\n                // 单个玩家，居中显示，正常大小\n                return [{x: 0, y: 0, scale: 1.0}];\n\n            case 2:\n                // 两个玩家，左右分布，缩放0.5\n                return [\n                    {x: -22, y: -8, scale: 0.5}, // 左\n                    {x: 22, y: -8, scale: 0.5}   // 右\n                ];\n\n            case 3:\n                // 三个玩家，上中下分布，缩放0.5\n                return [\n                    {x: 0, y: 12, scale: 0.5},    // 上\n                    {x: -23, y: -27, scale: 0.5}, // 左下\n                    {x: 23, y: -27, scale: 0.5}   // 右下\n                ];\n\n            case 4:\n                // 四个玩家，四角分布，缩放0.5\n                return [\n                    {x: -22, y: 12, scale: 0.5},  // 左上\n                    {x: 22, y: 12, scale: 0.5},   // 右上\n                    {x: -22, y: -30, scale: 0.5}, // 左下\n                    {x: 22, y: -30, scale: 0.5}   // 右下\n                ];\n\n            default:\n                // 超过4个玩家，只显示前4个\n                console.warn(`玩家数量过多: ${playerCount}，只显示前4个`);\n                return this.getHexPlayerPositions(4);\n        }\n    }\n\n    /**\n     * 调整自己的六边形头像位置和缩放（当多人在同一格子时）\n     * @param q 六边形q坐标\n     * @param r 六边形r坐标\n     * @param position 新的位置和缩放信息\n     * @param actions 其他玩家操作列表\n     */\n    private adjustMyHexAvatarPosition(q: number, r: number, position: {x: number, y: number, scale: number}, actions: PlayerActionDisplay[]) {\n        const key = this.getHexKey(q, r);\n        const gridData = this.hexGridData.get(key);\n\n        // 查找自己的头像节点\n        if (!gridData || !gridData.hasPlayer || !gridData.playerNode) {\n            console.warn(`在六边形位置(${q}, ${r})找不到自己的头像节点`);\n            return;\n        }\n\n        const myPlayerNode = gridData.playerNode;\n\n        // 计算该格子的总人数（自己 + 其他玩家）\n        const totalPlayers = 1 + (actions ? actions.length : 0);\n\n        // 计算基础位置（根据总人数决定是否偏移）\n        const basePosition = this.calculateHexBasePositionByPlayerCount(q, r, totalPlayers);\n\n        // 计算新的最终位置\n        const newPosition = cc.v2(\n            basePosition.x + position.x,\n            basePosition.y + position.y\n        );\n\n        // 播放平滑移动和缩放动画\n        this.playHexAvatarAdjustAnimation(myPlayerNode, newPosition, position.scale);\n    }\n\n    /**\n     * 根据六边形格子总人数计算基础位置（完全复制四边形棋盘控制器的逻辑）\n     * @param q 六边形q坐标\n     * @param r 六边形r坐标\n     * @param totalPlayers 该格子的总人数\n     * @returns 基础位置\n     */\n    private calculateHexBasePositionByPlayerCount(q: number, r: number, totalPlayers: number): cc.Vec2 {\n        if (totalPlayers === 1) {\n            // 一个格子里只有一个人：使用正常的偏移（单人头像预制体，y轴+20）\n            return this.getHexWorldPosition(q, r, true);\n        } else {\n            // 一个格子里有两个及以上：不偏移（多人头像预制体，不偏移）\n            return this.getHexWorldPosition(q, r, false);\n        }\n    }\n\n    /**\n     * 播放六边形头像调整动画（完全复制四边形棋盘控制器的逻辑）\n     * @param playerNode 玩家节点\n     * @param newPosition 新位置\n     * @param newScale 新缩放\n     */\n    private playHexAvatarAdjustAnimation(playerNode: cc.Node, newPosition: cc.Vec2, newScale: number) {\n        if (!playerNode || !playerNode.isValid) {\n            return;\n        }\n\n        // 停止之前的动画\n        playerNode.stopAllActions();\n\n        // 使用cc.tween播放位置和缩放动画\n        cc.tween(playerNode)\n            .to(0.3, {\n                x: newPosition.x,\n                y: newPosition.y,\n                scaleX: newScale,\n                scaleY: newScale\n            }, { easing: 'sineOut' })\n            .start();\n    }\n\n    /**\n     * 创建其他玩家在六边形位置的头像\n     * @param q 六边形q坐标\n     * @param r 六边形r坐标\n     * @param action 玩家操作数据\n     * @param position 位置和缩放信息\n     * @param totalPlayers 总玩家数\n     */\n    private createOtherPlayerAtHexPosition(q: number, r: number, action: PlayerActionDisplay, position: {x: number, y: number, scale: number}, totalPlayers: number) {\n        if (!this.playerGamePrefab || !this.boardNode) {\n            console.error(\"❌ 预制体或棋盘节点未设置！\");\n            return;\n        }\n\n        // 实例化玩家预制体\n        let playerNode = cc.instantiate(this.playerGamePrefab);\n\n        // 计算基础位置（根据总人数决定是否偏移）\n        const basePosition = this.calculateHexBasePositionByPlayerCount(q, r, totalPlayers);\n\n        // 计算最终位置\n        const finalPosition = cc.v2(\n            basePosition.x + position.x,\n            basePosition.y + position.y\n        );\n\n        playerNode.setPosition(finalPosition);\n\n        // 根据总人数设置缩放：单人0.8，多人使用position.scale\n        if (totalPlayers === 1) {\n            playerNode.setScale(0.8);\n        } else {\n            playerNode.setScale(position.scale);\n        }\n\n        // 先隐藏节点，等头像加载完成后再显示\n        playerNode.active = false;\n\n        // 添加到棋盘\n        this.addPlayerNodeSafely(playerNode);\n\n        // 设置其他玩家的头像和数据\n        this.setupOtherPlayerHexAvatar(playerNode, action, () => {\n            // 头像加载完成的回调，播放生成动画\n            this.playAvatarSpawnAnimation(playerNode);\n        });\n    }\n\n    /**\n     * 设置其他玩家的六边形头像和数据\n     * @param playerNode 玩家节点\n     * @param action 玩家操作数据\n     * @param onComplete 完成回调\n     */\n    private setupOtherPlayerHexAvatar(playerNode: cc.Node, action: PlayerActionDisplay, onComplete: () => void) {\n        // 查找PlayerGameController组件\n        let playerController = playerNode.getComponent(PlayerGameController);\n\n        if (playerController) {\n            // 在节点上存储userId，用于后续查找\n            playerNode['userId'] = action.userId;\n\n            // 设置旗子节点的显示状态\n            const withFlag = (action.action === 2); // action=2表示标记操作，显示旗子\n            if (playerController.flagNode) {\n                playerController.flagNode.active = withFlag;\n            }\n\n            // 获取真实的用户数据（和第一张地图逻辑一样）\n            let realUserData = this.getRealUserData(action.userId);\n            if (!realUserData) {\n                console.warn(`找不到用户 ${action.userId} 的真实数据，使用默认数据`);\n                // 使用默认数据作为备选\n                realUserData = {\n                    userId: action.userId,\n                    nickName: `玩家${action.userId}`,\n                    avatar: this.getDefaultAvatarUrl(),\n                    score: 0,\n                    pos: 0,\n                    coin: 0,\n                    status: 0,\n                    rank: 0\n                } as RoomUser;\n            }\n\n            // 使用PlayerGameController的setData方法来设置头像\n            try {\n                playerController.setData(realUserData);\n\n                // 延迟设置旗子状态，确保在PlayerGameController初始化之后\n                this.scheduleOnce(() => {\n                    if (playerController.flagNode) {\n                        playerController.flagNode.active = withFlag;\n                    }\n                    onComplete();\n                }, 0.1);\n            } catch (error) {\n                console.error(\"设置其他玩家头像数据失败:\", error);\n                onComplete();\n            }\n        } else {\n            console.warn(\"⚠️ 找不到PlayerGameController组件\");\n            onComplete();\n        }\n    }\n\n    /**\n     * 获取其他玩家的头像URL\n     * @param userId 用户ID\n     * @returns 头像URL\n     */\n    private getOtherPlayerAvatarUrl(userId: string): string {\n        // 这里可以根据userId获取真实的头像URL\n        // 暂时使用默认头像\n        return this.getDefaultAvatarUrl();\n    }\n\n\n\n\n\n    /**\n     * 从GlobalBean中获取真实的用户数据（和第一张地图逻辑完全一样）\n     * @param userId 用户ID\n     * @returns RoomUser 或 null\n     */\n    private getRealUserData(userId: string): RoomUser | null {\n        try {\n            if (!GlobalBean.GetInstance().noticeStartGame || !GlobalBean.GetInstance().noticeStartGame.users) {\n                console.warn(\"没有游戏数据，无法获取用户信息\");\n                return null;\n            }\n\n            const users = GlobalBean.GetInstance().noticeStartGame.users;\n            const user = users.find((u: RoomUser) => u.userId === userId);\n\n            if (user) {\n                return user;\n            } else {\n                console.warn(`未找到用户 ${userId} 的数据`);\n                return null;\n            }\n        } catch (error) {\n            console.error(`获取用户数据时出错: ${error}`);\n            return null;\n        }\n    }\n}\n"]}