
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/Chess/HexChessBoardController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'd9f2eirVKNNorhz2LDmkG2T', 'HexChessBoardController');
// scripts/game/Chess/HexChessBoardController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../../bean/GlobalBean");
var PlayerGameController_1 = require("../../pfb/PlayerGameController ");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var HexChessBoardController = /** @class */ (function (_super) {
    __extends(HexChessBoardController, _super);
    function HexChessBoardController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.playerGamePrefab = null;
        _this.boomPrefab = null;
        _this.biaojiPrefab = null;
        _this.boom1Prefab = null;
        _this.boom2Prefab = null;
        _this.boom3Prefab = null;
        _this.boom4Prefab = null;
        _this.boom5Prefab = null;
        _this.boom6Prefab = null;
        _this.boom7Prefab = null;
        _this.boom8Prefab = null;
        _this.boardNode = null; // 棋盘节点
        // 六边形棋盘配置
        _this.HEX_SIZE = 44; // 六边形半径
        _this.HEX_WIDTH = _this.HEX_SIZE * 2; // 六边形宽度
        _this.HEX_HEIGHT = _this.HEX_SIZE * Math.sqrt(3); // 六边形高度
        // 格子数据存储 - 使用Map存储六边形坐标
        _this.hexGridData = new Map(); // 存储六边形格子数据
        _this.hexGridNodes = new Map(); // 存储六边形格子节点
        _this.validHexCoords = []; // 有效的六边形坐标列表
        return _this;
    }
    HexChessBoardController.prototype.onLoad = function () {
    };
    HexChessBoardController.prototype.start = function () {
        var _this = this;
        // 延迟一帧后再次尝试启用触摸事件，确保所有节点都已创建完成
        this.scheduleOnce(function () {
            _this.setValidHexCoords([]); // 传入空数组，但会被忽略
            // 测试预制体位置计算
            _this.testHexPositionCalculation();
            _this.enableTouchForExistingGrids();
        }, 0.1);
    };
    /**
     * 设置有效的六边形坐标列表（忽略服务器数据，使用前端节点坐标）
     * @param _coords 服务器发送的坐标列表（将被忽略）
     */
    HexChessBoardController.prototype.setValidHexCoords = function (_coords) {
        // 忽略服务器传入的坐标，始终从节点名称自动生成
        this.generateCoordsFromNodeNames();
        this.initHexBoard();
    };
    /**
     * 从节点名称自动生成有效坐标列表
     */
    HexChessBoardController.prototype.generateCoordsFromNodeNames = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法生成坐标列表");
            return;
        }
        var foundCoords = [];
        var children = this.boardNode.children;
        var _loop_1 = function (i) {
            var child = children[i];
            var nodeName = child.name;
            // 跳过游戏元素节点，只处理六边形格子节点
            if (this_1.isGameElement(child, nodeName)) {
                return "continue";
            }
            var coords = this_1.parseHexCoordinateFromName(nodeName);
            if (coords) {
                // 检查是否已经存在相同的坐标
                var exists = foundCoords.some(function (c) { return c.q === coords.q && c.r === coords.r; });
                if (!exists) {
                    foundCoords.push({ q: coords.q, r: coords.r });
                }
            }
        };
        var this_1 = this;
        for (var i = 0; i < children.length; i++) {
            _loop_1(i);
        }
        this.validHexCoords = foundCoords;
    };
    // 初始化六边形棋盘
    HexChessBoardController.prototype.initHexBoard = function () {
        // 清空现有数据
        this.hexGridData.clear();
        this.hexGridNodes.clear();
        // 初始化有效坐标的数据
        for (var _i = 0, _a = this.validHexCoords; _i < _a.length; _i++) {
            var coord = _a[_i];
            var key = this.getHexKey(coord.q, coord.r);
            this.hexGridData.set(key, {
                q: coord.q,
                r: coord.r,
                worldPos: this.getHexWorldPosition(coord.q, coord.r),
                hasPlayer: false
            });
        }
        this.createHexGridNodes();
    };
    // 生成六边形坐标的唯一键
    HexChessBoardController.prototype.getHexKey = function (q, r) {
        return q + "," + r;
    };
    // 启用现有格子的触摸事件
    HexChessBoardController.prototype.createHexGridNodes = function () {
        if (!this.boardNode) {
            console.error("棋盘节点未设置！");
            return;
        }
        // 如果格子已经存在，直接启用触摸事件
        this.enableTouchForExistingGrids();
    };
    // 为现有格子启用触摸事件
    HexChessBoardController.prototype.enableTouchForExistingGrids = function () {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法启用触摸事件！");
            return;
        }
        // 遍历棋盘节点的所有子节点
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            var nodeName = child.name;
            // 跳过游戏元素节点，只处理六边形格子节点
            if (this.isGameElement(child, nodeName)) {
                continue;
            }
            // 尝试从节点名称解析六边形坐标
            var coords = this.parseHexCoordinateFromName(nodeName);
            if (coords) {
                this.setupHexGridTouchEvents(child, coords.q, coords.r);
                var key = this.getHexKey(coords.q, coords.r);
                this.hexGridNodes.set(key, child);
            }
            else {
                // 如果无法从名称解析，尝试从位置计算
                var pos = child.getPosition();
                var coords_1 = this.getHexCoordinateFromPosition(pos);
                if (coords_1) {
                    this.setupHexGridTouchEvents(child, coords_1.q, coords_1.r);
                    var key = this.getHexKey(coords_1.q, coords_1.r);
                    this.hexGridNodes.set(key, child);
                }
            }
        }
    };
    // 从节点名称解析六边形坐标
    HexChessBoardController.prototype.parseHexCoordinateFromName = function (nodeName) {
        var patterns = [
            /^sixblock_(-?\d+)_(-?\d+)$/,
        ];
        for (var _i = 0, patterns_1 = patterns; _i < patterns_1.length; _i++) {
            var pattern = patterns_1[_i];
            var match = nodeName.match(pattern);
            if (match) {
                var coords = { q: parseInt(match[1]), r: parseInt(match[2]) };
                return coords;
            }
        }
        console.warn("\u274C \u65E0\u6CD5\u89E3\u6790\u8282\u70B9\u540D\u79F0: " + nodeName);
        return null;
    };
    // 从位置计算六边形坐标（近似）
    HexChessBoardController.prototype.getHexCoordinateFromPosition = function (pos) {
        // 六边形坐标转换（从像素坐标到六边形坐标）
        var x = pos.x;
        var y = pos.y;
        // 使用六边形坐标转换公式
        var q = Math.round((Math.sqrt(3) / 3 * x - 1 / 3 * y) / this.HEX_SIZE);
        var r = Math.round((2 / 3 * y) / this.HEX_SIZE);
        // 检查是否为有效坐标
        if (this.isValidHexCoordinate(q, r)) {
            return { q: q, r: r };
        }
        return null;
    };
    // 计算六边形预制体的生成位置（直接使用您提供的格子中心坐标）
    HexChessBoardController.prototype.getHexWorldPosition = function (q, r, isPlayerAvatar) {
        if (isPlayerAvatar === void 0) { isPlayerAvatar = false; }
        // 您提供的精确格子中心坐标
        var exactCoords = new Map();
        // 更新后的基准点坐标（与rowData保持一致）
        exactCoords.set("0,0", cc.v2(-300, -258)); // r=0行基准点
        exactCoords.set("1,-1", cc.v2(-258, -184)); // r=-1行基准点
        exactCoords.set("1,-2", cc.v2(-300, -108)); // r=-2行基准点
        exactCoords.set("2,-3", cc.v2(-258, -36)); // r=-3行基准点
        exactCoords.set("2,-4", cc.v2(-300, 37)); // r=-4行基准点
        exactCoords.set("3,-5", cc.v2(-258, 110)); // r=-5行基准点
        exactCoords.set("3,-6", cc.v2(-300, 185)); // r=-6行基准点
        exactCoords.set("4,-7", cc.v2(-258, 260)); // r=-7行基准点
        // 首先检查是否有精确坐标
        var key = q + "," + r;
        if (exactCoords.has(key)) {
            var pos = exactCoords.get(key);
            // 如果是单人头像预制体，往左上偏移一点点
            if (isPlayerAvatar) {
                return cc.v2(pos.x, pos.y - 12); // 改为往左偏移10像素
            }
            return pos;
        }
        // 对于其他坐标，使用基于您提供的精确坐标数据进行计算
        // 定义每一行的数据：使用统一步长86，保证美观整齐
        var UNIFORM_STEP_X = 86; // 统一的x方向步长
        var rowData = new Map();
        // 基于您提供的更新数据，使用统一步长86
        rowData.set(0, { baseQ: 0, baseX: -300, y: -258 }); // r=0行：基准点(0,0) → (-300, -258)
        rowData.set(-1, { baseQ: 1, baseX: -258, y: -184 }); // r=-1行：基准点(1,-1) → (-258, -184)
        rowData.set(-2, { baseQ: 1, baseX: -300, y: -108 }); // r=-2行：基准点(1,-2) → (-300, -108)
        rowData.set(-3, { baseQ: 2, baseX: -258, y: -36 }); // r=-3行：基准点(2,-3) → (-258, -36)
        rowData.set(-4, { baseQ: 2, baseX: -300, y: 37 }); // r=-4行：基准点(2,-4) → (-300, 37)
        rowData.set(-5, { baseQ: 3, baseX: -258, y: 110 }); // r=-5行：基准点(3,-5) → (-258, 110)
        rowData.set(-6, { baseQ: 3, baseX: -300, y: 185 }); // r=-6行：基准点(3,-6) → (-300, 185)
        rowData.set(-7, { baseQ: 4, baseX: -258, y: 260 }); // r=-7行：基准点(4,-7) → (-258, 260)
        // 计算基础位置
        var x, y;
        // 如果有该行的数据，使用统一步长计算
        if (rowData.has(r)) {
            var data = rowData.get(r);
            x = data.baseX + (q - data.baseQ) * UNIFORM_STEP_X;
            y = data.y;
        }
        else {
            // 对于其他行，使用通用的六边形轴线坐标系公式（也使用统一步长）
            var baseX = -300; // 更新为新的基准点
            var baseY = -258;
            var stepXR = -43;
            var stepYR = 74;
            x = baseX + q * UNIFORM_STEP_X + r * stepXR;
            y = baseY - r * stepYR;
        }
        // 如果是单人头像预制体，往左上偏移一点点
        if (isPlayerAvatar) {
            y -= 12; // 往下偏移12像素（相比之前的-20，现在是-12，相当于往上调了8像素）
        }
        return cc.v2(x, y);
    };
    // 为六边形格子节点设置触摸事件
    HexChessBoardController.prototype.setupHexGridTouchEvents = function (gridNode, q, r) {
        var _this = this;
        // 安全检查：确保坐标有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.error("\u274C setupHexGridTouchEvents: \u5C1D\u8BD5\u4E3A\u65E0\u6548\u5750\u6807(" + q + "," + r + ")\u8BBE\u7F6E\u89E6\u6478\u4E8B\u4EF6");
            return;
        }
        // 长按相关变量
        var isLongPressing = false;
        var longPressTimer = 0;
        var longPressCallback = null;
        var LONG_PRESS_TIME = 1.0; // 1秒长按时间
        // 触摸开始事件
        gridNode.on(cc.Node.EventType.TOUCH_START, function (_event) {
            isLongPressing = true;
            longPressTimer = 0;
            // 开始长按检测
            longPressCallback = function () {
                if (isLongPressing) {
                    longPressTimer += 0.1;
                    if (longPressTimer >= LONG_PRESS_TIME) {
                        _this.onHexGridLongPress(q, r);
                        isLongPressing = false;
                        if (longPressCallback) {
                            _this.unschedule(longPressCallback);
                        }
                    }
                }
            };
            _this.schedule(longPressCallback, 0.1);
        }, this);
        // 触摸结束事件
        gridNode.on(cc.Node.EventType.TOUCH_END, function (event) {
            // 如果不是长按，则执行点击事件
            if (isLongPressing && longPressTimer < LONG_PRESS_TIME) {
                _this.onHexGridClick(q, r, event);
            }
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 触摸取消事件
        gridNode.on(cc.Node.EventType.TOUCH_CANCEL, function (_event) {
            isLongPressing = false;
            if (longPressCallback) {
                _this.unschedule(longPressCallback);
            }
        }, this);
        // 添加Button组件以确保触摸响应
        var button = gridNode.getComponent(cc.Button);
        if (!button) {
            button = gridNode.addComponent(cc.Button);
            button.transition = cc.Button.Transition.SCALE;
            button.zoomScale = 0.95;
        }
    };
    // 六边形格子点击事件 - 发送挖掘操作
    HexChessBoardController.prototype.onHexGridClick = function (q, r, _event) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有玩家预制体
        if (gridData && gridData.hasPlayer) {
            console.warn("\u26A0\uFE0F \u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u73A9\u5BB6");
            return;
        }
        // 发送挖掘操作事件 (action = 1)
        this.node.emit('hex-chess-board-click', {
            q: q,
            r: r,
            action: 1 // 1 = 挖掘
        });
    };
    // 六边形格子长按事件 - 发送标记操作
    HexChessBoardController.prototype.onHexGridLongPress = function (q, r) {
        // 检查坐标是否有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u274C \u65E0\u6548\u7684\u516D\u8FB9\u5F62\u5750\u6807: (" + q + ", " + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 检查该位置是否已经有玩家预制体
        if (gridData && gridData.hasPlayer) {
            console.warn("\u26A0\uFE0F \u683C\u5B50(" + q + ", " + r + ")\u5DF2\u6709\u73A9\u5BB6");
            return;
        }
        // 发送标记操作事件 (action = 2)
        this.node.emit('hex-chess-board-click', {
            q: q,
            r: r,
            action: 2 // 2 = 标记
        });
    };
    // 检查六边形坐标是否有效
    HexChessBoardController.prototype.isValidHexCoordinate = function (q, r) {
        var key = this.getHexKey(q, r);
        return this.hexGridData.has(key);
    };
    // 在六边形格子上放置玩家预制体
    HexChessBoardController.prototype.placePlayerOnHexGrid = function (q, r, withFlag) {
        var _this = this;
        if (withFlag === void 0) { withFlag = false; }
        // 双重检查：确保坐标有效
        if (!this.isValidHexCoordinate(q, r)) {
            console.error("\u274C placePlayerOnHexGrid: \u65E0\u6548\u5750\u6807(" + q + "," + r + ")");
            return;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 双重检查：确保格子为空
        if (!gridData || gridData.hasPlayer) {
            console.error("\u274C placePlayerOnHexGrid: \u683C\u5B50(" + q + "," + r + ")\u5DF2\u6709\u73A9\u5BB6\uFF0C\u4E0D\u80FD\u91CD\u590D\u653E\u7F6E");
            return;
        }
        if (!this.playerGamePrefab) {
            console.error("❌ 玩家预制体未设置！");
            return;
        }
        if (!this.boardNode) {
            console.error("❌ 棋盘节点未设置！");
            return;
        }
        // 实例化玩家预制体
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 计算正确的位置（单人头像预制体，y轴+20）
        var correctPosition = this.getHexWorldPosition(q, r, true);
        playerNode.setPosition(correctPosition);
        // 设置单人放置的缩放为0.8
        playerNode.setScale(0.8);
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 处理Layout限制问题
        this.addPlayerNodeSafely(playerNode);
        // 设置头像和用户数据（异步加载）
        this.setupPlayerAvatarAsync(playerNode, q, r, withFlag, function () {
            // 头像加载完成的回调，播放生成动画
            _this.playAvatarSpawnAnimation(playerNode);
        });
        // 更新格子数据
        gridData.hasPlayer = true;
        gridData.playerNode = playerNode;
    };
    // 安全地添加玩家节点（处理Layout限制）
    HexChessBoardController.prototype.addPlayerNodeSafely = function (playerNode) {
        // 检查棋盘节点是否存在
        if (!this.boardNode) {
            console.error("棋盘节点未设置，无法添加玩家节点！");
            return;
        }
        // 检查棋盘节点是否有Layout组件
        var layout = this.boardNode.getComponent(cc.Layout);
        if (layout) {
            // 临时禁用Layout
            layout.enabled = false;
            // 添加节点
            this.boardNode.addChild(playerNode);
        }
        else {
            this.boardNode.addChild(playerNode);
        }
    };
    // 异步设置玩家头像（带回调）
    HexChessBoardController.prototype.setupPlayerAvatarAsync = function (playerNode, q, r, withFlag, onComplete) {
        var _a, _b;
        // 查找PlayerGameController组件（使用类引用）
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (playerController) {
            // 检查avatar节点是否存在
            if (playerController.avatar) {
                // 检查avatar节点是否有Sprite组件
                var avatarSprite = playerController.avatar.getComponent(cc.Sprite);
                if (!avatarSprite) {
                    avatarSprite = playerController.avatar.addComponent(cc.Sprite);
                }
                // 确保avatar节点可见
                playerController.avatar.active = true;
                playerController.avatar.opacity = 255;
            }
            else {
                console.error("❌ PlayerGameController中的avatar节点为null");
                onComplete();
                return;
            }
            // 设置旗子节点的显示状态
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag;
                // 额外检查旗子节点的可见性
                if (withFlag) {
                    playerController.flagNode.opacity = 255;
                    // 确保旗子节点的父节点也是可见的
                    var parent = playerController.flagNode.parent;
                    while (parent && parent !== playerNode) {
                        parent.active = true;
                        parent = parent.parent;
                    }
                }
            }
            else {
                console.warn("\u26A0\uFE0F \u627E\u4E0D\u5230\u65D7\u5B50\u8282\u70B9 (" + q + "," + r + ")");
            }
            // 获取当前用户ID
            var currentUserId = ((_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId) || "hex_player_" + q + "_" + r;
            // 在节点上存储userId，用于后续查找
            playerNode['userId'] = currentUserId;
            // 创建用户数据并设置头像
            var userData = {
                userId: currentUserId,
                nickName: "\u73A9\u5BB6(" + q + "," + r + ")",
                avatar: this.getDefaultAvatarUrl(),
                score: 0,
                pos: 0,
                coin: 0,
                status: 0,
                rank: 0
            };
            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(userData);
                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(function () {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag;
                    }
                    onComplete();
                }, 0.1);
            }
            catch (error) {
                console.error("设置头像数据失败:", error);
                onComplete();
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            onComplete();
        }
    };
    // 获取默认头像URL
    HexChessBoardController.prototype.getDefaultAvatarUrl = function () {
        // 使用真实的头像URL
        return "https://static.gooplay.com/online/user-avatar/1732786296530322669.jpg";
    };
    /**
     * 播放头像生成动画（由大变小，完全复制四边形棋盘控制器的逻辑）
     * @param playerNode 玩家节点
     */
    HexChessBoardController.prototype.playAvatarSpawnAnimation = function (playerNode) {
        if (!playerNode) {
            console.warn("播放生成动画失败：节点为空");
            return;
        }
        // 显示节点
        playerNode.active = true;
        // 设置初始缩放为1.5倍（比正常大）
        var originalScale = playerNode.scaleX;
        var startScale = originalScale * 1.5;
        playerNode.setScale(startScale);
        // 使用cc.Tween创建由大变小的缩放动画
        cc.tween(playerNode)
            .to(0.3, { scaleX: originalScale, scaleY: originalScale }, { easing: 'backOut' })
            .start();
    };
    // 清除指定六边形格子的玩家
    HexChessBoardController.prototype.clearHexGridPlayer = function (q, r) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (!gridData || !gridData.hasPlayer) {
            return false;
        }
        // 移除玩家节点
        if (gridData.playerNode) {
            gridData.playerNode.removeFromParent();
            gridData.playerNode = null;
        }
        // 更新数据
        gridData.hasPlayer = false;
        return true;
    };
    // 清除所有玩家
    HexChessBoardController.prototype.clearAllPlayers = function () {
        var clearedCount = 0;
        // 1. 清理存储在hexGridData中的玩家节点（自己的头像）
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                gridData.playerNode.removeFromParent();
                gridData.playerNode = null;
                gridData.hasPlayer = false;
                clearedCount++;
            }
        });
        // 2. 清理棋盘上的其他玩家头像节点
        if (this.boardNode) {
            var children = this.boardNode.children.slice(); // 创建副本避免遍历时修改数组
            for (var i = 0; i < children.length; i++) {
                var child = children[i];
                if (child.name === "player_game_pfb") {
                    // 检查是否有PlayerGameController组件
                    var playerController = child.getComponent(PlayerGameController_1.default);
                    if (playerController) {
                        child.removeFromParent();
                        clearedCount++;
                    }
                }
            }
        }
    };
    /**
     * 清理所有玩家预制体（新回合开始时调用）
     * 包括自己的头像和其他玩家的头像
     * 为了与四边形棋盘控制器保持一致的接口
     */
    HexChessBoardController.prototype.clearAllPlayerNodes = function () {
        this.clearAllPlayers();
    };
    // 获取所有已放置玩家的六边形坐标
    HexChessBoardController.prototype.getAllPlayerHexCoordinates = function () {
        var coordinates = [];
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer) {
                coordinates.push({ q: gridData.q, r: gridData.r });
            }
        });
        return coordinates;
    };
    // 检查六边形格子是否为空
    HexChessBoardController.prototype.isHexGridEmpty = function (q, r) {
        if (!this.isValidHexCoordinate(q, r)) {
            return false;
        }
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        return gridData ? !gridData.hasPlayer : false;
    };
    /**
     * 重置游戏场景（游戏开始时调用）
     * 清除数字、炸弹、标记预制体，重新显示所有小格子
     */
    HexChessBoardController.prototype.resetGameScene = function () {
        if (!this.boardNode) {
            console.error("❌ 棋盘节点不存在，无法重置");
            return;
        }
        // 清除所有游戏元素（数字、炸弹、标记等）
        this.clearAllGameElements();
        // 显示所有小格子
        this.showAllHexGrids();
        // 重新初始化棋盘数据
        this.reinitializeHexBoardData();
    };
    /**
     * 清除所有游戏元素（数字、炸弹、标记、玩家头像等），但保留小格子
     */
    HexChessBoardController.prototype.clearAllGameElements = function () {
        if (!this.boardNode) {
            return;
        }
        var childrenToRemove = [];
        // 遍历棋盘的所有子节点
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            var nodeName = child.name;
            // 检查是否是需要清除的游戏元素（不包括小格子）
            if (this.isGameElement(child, nodeName)) {
                childrenToRemove.push(child);
            }
        }
        // 移除找到的游戏元素
        childrenToRemove.forEach(function (child) {
            child.removeFromParent();
        });
    };
    /**
     * 判断节点是否是游戏元素（需要清除的），小格子不会被清除
     */
    HexChessBoardController.prototype.isGameElement = function (node, nodeName) {
        // 绝对不清除的节点（六边形小格子）
        if (nodeName.startsWith("HexGrid_") || nodeName === "hexblock") {
            return false;
        }
        // 分数控制器不清除
        if (nodeName.includes("Score") || nodeName.includes("score")) {
            return false;
        }
        // UI相关节点不清除
        if (nodeName.includes("UI") || nodeName.includes("ui")) {
            return false;
        }
        // 明确需要清除的游戏预制体
        // 炸弹预制体
        if (nodeName === "Boom") {
            return true;
        }
        // 数字预制体（Boom1, Boom2, Boom3 等，以及 HexBoom1, HexBoom2 等）
        if (nodeName.match(/^(Hex)?Boom\d+$/)) {
            return true;
        }
        // 临时数字节点（NeighborMines_1, NeighborMines_2 等）
        if (nodeName.match(/^NeighborMines_\d+$/)) {
            return true;
        }
        // 测试节点（Test_q_r 格式）
        if (nodeName.match(/^Test_-?\d+_-?\d+$/)) {
            return true;
        }
        // 玩家预制体（通过组件判断）
        if (node.getComponent(PlayerGameController_1.default)) {
            return true;
        }
        // 标记预制体
        if (nodeName.includes("Flag") || nodeName.includes("Mark") || nodeName.includes("flag") ||
            nodeName === "Biaoji" || nodeName.includes("Biaoji")) {
            return true;
        }
        // 玩家头像预制体
        if (nodeName.includes("Player") || nodeName.includes("Avatar")) {
            return true;
        }
        // 默认保留未知节点（保守策略）
        return false;
    };
    /**
     * 显示所有六边形小格子（第二把游戏开始时恢复被隐藏的小格子）
     */
    HexChessBoardController.prototype.showAllHexGrids = function () {
        if (!this.boardNode) {
            return;
        }
        // 遍历棋盘的所有子节点，找到小格子并显示
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 如果是六边形小格子节点
            if (child.name.startsWith("HexGrid_") || child.name === "hexblock") {
                // 停止所有可能正在进行的动画
                child.stopAllActions();
                // 恢复显示状态
                child.active = true;
                child.opacity = 255;
                child.scaleX = 1;
                child.scaleY = 1;
                // 确保格子可以交互
                var button = child.getComponent(cc.Button);
                if (button) {
                    button.enabled = true;
                }
            }
        }
    };
    /**
     * 隐藏指定位置的六边形小格子（点击时调用）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param immediate 是否立即隐藏（不播放动画）
     */
    HexChessBoardController.prototype.hideHexGridAt = function (q, r, immediate) {
        if (immediate === void 0) { immediate = false; }
        if (!this.isValidHexCoordinate(q, r)) {
            console.warn("\u9690\u85CF\u683C\u5B50\u5931\u8D25\uFF1A\u5750\u6807(" + q + ", " + r + ")\u65E0\u6548");
            return;
        }
        // 获取格子节点
        var key = this.getHexKey(q, r);
        var gridNode = this.hexGridNodes.get(key);
        if (gridNode) {
            if (immediate) {
                // 立即隐藏，不播放动画
                gridNode.active = false;
            }
            else {
                // 使用动画隐藏格子
                cc.tween(gridNode)
                    .to(0.3, { opacity: 0, scaleX: 0, scaleY: 0 }, { easing: 'sineIn' })
                    .call(function () {
                    gridNode.active = false;
                })
                    .start();
            }
        }
    };
    /**
     * 重新初始化六边形棋盘数据
     */
    HexChessBoardController.prototype.reinitializeHexBoardData = function () {
        // 重置hexGridData中的玩家状态
        this.hexGridData.forEach(function (gridData) {
            gridData.hasPlayer = false;
            gridData.playerNode = null;
        });
    };
    /**
     * 获取六边形格子数据
     */
    HexChessBoardController.prototype.getHexGridData = function (q, r) {
        var key = this.getHexKey(q, r);
        return this.hexGridData.get(key) || null;
    };
    /**
     * 批量放置玩家（用于从服务器同步数据）
     */
    HexChessBoardController.prototype.batchPlaceHexPlayers = function (coordinates) {
        var _this = this;
        coordinates.forEach(function (coord) {
            if (_this.isValidHexCoordinate(coord.q, coord.r) && _this.isHexGridEmpty(coord.q, coord.r)) {
                _this.placePlayerOnHexGrid(coord.q, coord.r);
            }
        });
    };
    /**
     * 测试点击功能（调试用）
     */
    HexChessBoardController.prototype.testHexClick = function (q, r) {
        this.onHexGridClick(q, r);
    };
    /**
     * 获取棋盘状态信息（调试用）
     */
    HexChessBoardController.prototype.getHexBoardInfo = function () {
        var info = {
            validHexCoordsCount: this.validHexCoords.length,
            boardNodeChildren: this.boardNode ? this.boardNode.children.length : 0,
            playerCount: this.getAllPlayerHexCoordinates().length,
            hasPlayerGamePrefab: !!this.playerGamePrefab,
            hasBoardNode: !!this.boardNode,
            hexGridDataSize: this.hexGridData.size,
            hexGridNodesSize: this.hexGridNodes.size
        };
        return info;
    };
    /**
     * 获取前端节点的总数量（用于计算炸弹数量）
     */
    HexChessBoardController.prototype.getHexGridCount = function () {
        return this.validHexCoords.length;
    };
    /**
     * 根据前端节点数量计算推荐的炸弹数量
     */
    HexChessBoardController.prototype.getRecommendedMineCount = function () {
        var gridCount = this.getHexGridCount();
        if (gridCount === 0) {
            return 13; // 默认值
        }
        // 约15%的格子是炸弹
        var mineCount = Math.floor(gridCount * 0.15);
        return Math.max(mineCount, 5); // 至少5个炸弹
    };
    /**
     * 测试六边形预制体位置计算是否正确
     */
    HexChessBoardController.prototype.testHexPositionCalculation = function () {
        var _this = this;
        // 测试更新后的基准点坐标
        var testPoints = [
            { q: 0, r: 0, expected: { x: -300, y: -258 }, desc: "r=0行基准点(0,0)" },
            { q: 1, r: -1, expected: { x: -258, y: -184 }, desc: "r=-1行基准点(1,-1)" },
            { q: 1, r: -2, expected: { x: -300, y: -108 }, desc: "r=-2行基准点(1,-2)" },
            { q: 2, r: -3, expected: { x: -258, y: -36 }, desc: "r=-3行基准点(2,-3)" },
            { q: 2, r: -4, expected: { x: -300, y: 37 }, desc: "r=-4行基准点(2,-4)" },
            { q: 3, r: -5, expected: { x: -258, y: 110 }, desc: "r=-5行基准点(3,-5)" },
            { q: 3, r: -6, expected: { x: -300, y: 185 }, desc: "r=-6行基准点(3,-6)" },
            { q: 4, r: -7, expected: { x: -258, y: 260 }, desc: "r=-7行基准点(4,-7)" }
        ];
        var correctCount = 0;
        testPoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
            var errorX = Math.abs(calculated.x - point.expected.x);
            var errorY = Math.abs(calculated.y - point.expected.y);
            var isCorrect = errorX < 2 && errorY < 2; // 允许2像素误差
            if (isCorrect)
                correctCount++;
        });
        // 测试一些中间坐标
        var intermediatePoints = [
            // r=0行测试
            { q: 2, r: 0 }, { q: 3, r: 0 }, { q: 4, r: 0 }, { q: 5, r: 0 }, { q: 6, r: 0 },
            // r=-1行测试
            { q: 3, r: -1 }, { q: 4, r: -1 }, { q: 5, r: -1 }, { q: 6, r: -1 },
            // r=-2行测试
            { q: 3, r: -2 }, { q: 4, r: -2 }, { q: 5, r: -2 }, { q: 6, r: -2 }, { q: 7, r: -2 },
            // r=-3行测试
            { q: 4, r: -3 }, { q: 5, r: -3 }, { q: 6, r: -3 }, { q: 7, r: -3 }
        ];
        intermediatePoints.forEach(function (point) {
            var calculated = _this.getHexWorldPosition(point.q, point.r);
        });
        // 暴露到全局以便调试
        window.testHexPositions = function () { return _this.testHexPositionCalculation(); };
    };
    // ==================== NoticeActionDisplay 相关方法 ====================
    // 以下方法与第一张地图（方形地图）的逻辑完全一样，用于处理加分和掀开地图
    /**
     * 在指定六边形位置创建boom预制体
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param isCurrentUser 是否是当前用户点到的雷
     */
    HexChessBoardController.prototype.createHexBoomPrefab = function (q, r, isCurrentUser) {
        if (isCurrentUser === void 0) { isCurrentUser = true; }
        if (!this.boomPrefab) {
            console.error("boomPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化boom预制体
        var boomNode = cc.instantiate(this.boomPrefab);
        boomNode.name = "HexBoom";
        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        var position = this.getHexWorldPosition(q, r, false);
        boomNode.setPosition(position);
        // 添加到棋盘
        this.boardNode.addChild(boomNode);
        // 播放出现动画
        boomNode.setScale(0);
        cc.tween(boomNode)
            .to(0.3, { scaleX: 1.2, scaleY: 1.2 }, { easing: 'backOut' })
            .to(0.1, { scaleX: 1.0, scaleY: 1.0 })
            .start();
        // 只有当前用户点到雷时才播放棋盘震动效果
        if (isCurrentUser) {
            this.playBoardShakeAnimation();
        }
    };
    /**
     * 在指定六边形位置创建biaoji预制体
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     */
    HexChessBoardController.prototype.createHexBiaojiPrefab = function (q, r) {
        if (!this.biaojiPrefab) {
            console.error("biaojiPrefab 预制体未设置，请在编辑器中挂载");
            return;
        }
        // 实例化biaoji预制体
        var biaojiNode = cc.instantiate(this.biaojiPrefab);
        biaojiNode.name = "HexBiaoji";
        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        var position = this.getHexWorldPosition(q, r, false);
        biaojiNode.setPosition(position);
        // 添加到棋盘
        this.boardNode.addChild(biaojiNode);
        // 播放出现动画
        biaojiNode.setScale(0);
        cc.tween(biaojiNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 更新指定六边形位置的neighborMines显示
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param neighborMines 周围地雷数量
     */
    HexChessBoardController.prototype.updateHexNeighborMinesDisplay = function (q, r, neighborMines) {
        // 0不需要显示数字
        if (neighborMines === 0) {
            return;
        }
        // 直接使用boom数字预制体
        this.createHexNumberPrefab(q, r, neighborMines);
    };
    /**
     * 创建六边形数字预制体（boom1, boom2, ...）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param number 数字
     */
    HexChessBoardController.prototype.createHexNumberPrefab = function (q, r, number) {
        // 根据数字选择对应的预制体
        var prefab = null;
        switch (number) {
            case 1:
                prefab = this.boom1Prefab;
                break;
            case 2:
                prefab = this.boom2Prefab;
                break;
            case 3:
                prefab = this.boom3Prefab;
                break;
            case 4:
                prefab = this.boom4Prefab;
                break;
            case 5:
                prefab = this.boom5Prefab;
                break;
            case 6:
                prefab = this.boom6Prefab;
                break;
            case 7:
                prefab = this.boom7Prefab;
                break;
            case 8:
                prefab = this.boom8Prefab;
                break;
            default:
                console.error("\u4E0D\u652F\u6301\u7684\u6570\u5B57: " + number);
                return;
        }
        if (!prefab) {
            console.error("boom" + number + "Prefab \u9884\u5236\u4F53\u672A\u8BBE\u7F6E\uFF0C\u8BF7\u5728\u7F16\u8F91\u5668\u4E2D\u6302\u8F7D");
            return;
        }
        // 实例化数字预制体
        var numberNode = cc.instantiate(prefab);
        numberNode.name = "HexBoom" + number;
        // 设置位置（使用六边形坐标计算，不是单人头像所以不偏移）
        var position = this.getHexWorldPosition(q, r, false);
        numberNode.setPosition(position);
        // 添加到棋盘
        this.boardNode.addChild(numberNode);
        // 播放出现动画
        numberNode.setScale(0);
        cc.tween(numberNode)
            .to(0.2, { scaleX: 1.0, scaleY: 1.0 }, { easing: 'backOut' })
            .start();
    };
    /**
     * 播放棋盘震动动画（当前用户点到雷时）
     */
    HexChessBoardController.prototype.playBoardShakeAnimation = function () {
        if (!this.boardNode) {
            return;
        }
        var originalPosition = this.boardNode.getPosition();
        var shakeDistance = 10;
        cc.tween(this.boardNode)
            .to(0.05, { x: originalPosition.x + shakeDistance })
            .to(0.05, { x: originalPosition.x - shakeDistance })
            .to(0.05, { x: originalPosition.x + shakeDistance })
            .to(0.05, { x: originalPosition.x - shakeDistance })
            .to(0.05, { x: originalPosition.x })
            .start();
    };
    // ==================== 头像生命周期管理 ====================
    // 以下方法与第一张地图的逻辑完全一样，用于管理头像预制体的生命周期
    /**
     * 让所有六边形头像消失（和第一张地图的hideAvatarsAtPosition逻辑一样）
     * @param onComplete 完成回调
     */
    HexChessBoardController.prototype.hideAllHexAvatars = function (onComplete) {
        var _this = this;
        if (!this.boardNode) {
            console.warn("棋盘节点不存在，无法清理六边形头像");
            onComplete();
            return;
        }
        // 收集所有头像节点（参考第一张地图的逻辑）
        var avatarNodes = [];
        // 方法1: 收集存储在hexGridData中的玩家节点（自己的头像）
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                avatarNodes.push(gridData.playerNode);
            }
        });
        // 方法2: 收集棋盘上所有的玩家预制体节点（包括其他玩家的头像）
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 检查是否是玩家预制体（通过组件判断）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                // 避免重复添加（可能已经在方法1中添加过）
                if (!avatarNodes.includes(child)) {
                    avatarNodes.push(child);
                }
            }
        }
        // 如果没有头像，直接执行回调
        if (avatarNodes.length === 0) {
            this.clearAllMyHexAvatarReferences();
            onComplete();
            return;
        }
        var completedCount = 0;
        var totalCount = avatarNodes.length;
        // 为每个头像播放消失动画（和第一张地图完全一样）
        avatarNodes.forEach(function (avatarNode) {
            // 使用cc.Tween播放消失动画
            cc.tween(avatarNode)
                .to(0.3, { opacity: 0, scaleX: 0.5, scaleY: 0.5 }, { easing: 'sineIn' })
                .call(function () {
                // 动画完成后移除节点
                avatarNode.removeFromParent();
                completedCount++;
                // 所有头像都消失完成后，执行回调
                if (completedCount >= totalCount) {
                    // 清除所有自己头像的引用
                    _this.clearAllMyHexAvatarReferences();
                    onComplete();
                }
            })
                .start();
        });
    };
    /**
     * 清除所有自己六边形头像的引用（和第一张地图的clearAllMyAvatarReferences逻辑一样）
     */
    HexChessBoardController.prototype.clearAllMyHexAvatarReferences = function () {
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer) {
                gridData.hasPlayer = false;
                gridData.playerNode = null;
            }
        });
    };
    // ==================== 加分逻辑相关方法 ====================
    // 以下方法与第一张地图的加分逻辑完全一样
    /**
     * 在指定六边形位置的玩家节点上显示分数
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param score 分数
     * @param showPlusOne 是否显示+1（先手奖励）
     */
    HexChessBoardController.prototype.showScoreOnHexPlayerNode = function (q, r, score, showPlusOne) {
        var _this = this;
        // 查找该位置的玩家节点
        var playerNode = this.findHexPlayerNodeAtPosition(q, r);
        if (!playerNode) {
            // 在NoticeActionDisplay流程中，头像会被清理，找不到节点是正常的
            return;
        }
        // 获取PlayerGameController组件
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (!playerController) {
            console.warn("找不到PlayerGameController组件");
            return;
        }
        // 显示分数动画
        if (showPlusOne) {
            // 先显示+1，再显示本回合得分
            this.showScoreAnimationOnHexNode(playerController, 1, function () {
                _this.scheduleOnce(function () {
                    _this.showScoreAnimationOnHexNode(playerController, score, null);
                }, 1.0);
            });
        }
        else {
            // 只显示本回合得分
            this.showScoreAnimationOnHexNode(playerController, score, null);
        }
    };
    /**
     * 查找指定六边形位置的玩家节点
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @returns 玩家节点或null
     */
    HexChessBoardController.prototype.findHexPlayerNodeAtPosition = function (q, r) {
        // 方法1: 从hexGridData中查找（自己的头像）
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData && gridData.hasPlayer && gridData.playerNode) {
            return gridData.playerNode;
        }
        // 方法2: 在棋盘上查找其他玩家的头像
        if (!this.boardNode) {
            return null;
        }
        // 遍历棋盘上的所有子节点，查找player_game_pfb
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            if (child.name === "player_game_pfb") {
                // 检查位置是否匹配（允许一定的误差）
                var expectedPos = this.getHexWorldPosition(q, r, true);
                var actualPos = child.getPosition();
                var distance = expectedPos.sub(actualPos).mag();
                if (distance < 10) { // 10像素误差范围内
                    return child;
                }
            }
        }
        return null;
    };
    /**
     * 在六边形节点上显示分数动画
     * @param playerController 玩家控制器
     * @param score 分数
     * @param onComplete 完成回调
     */
    HexChessBoardController.prototype.showScoreAnimationOnHexNode = function (playerController, score, onComplete) {
        // 调用PlayerGameController的showAddScore方法
        if (playerController && typeof playerController.showAddScore === 'function') {
            playerController.showAddScore(score);
        }
        if (onComplete) {
            this.scheduleOnce(onComplete, 1.0);
        }
    };
    /**
     * 显示玩家游戏加减分效果（完全复制四边形棋盘控制器的逻辑）
     * @param userId 用户ID
     * @param score 分数变化（正数为加分，负数为减分）
     */
    HexChessBoardController.prototype.showHexPlayerGameScore = function (userId, score) {
        var currentUserId = this.getCurrentHexUserId();
        var foundPlayer = false;
        // 1. 如果是当前用户，查找自己的玩家节点（存储在hexGridData中）
        if (userId === currentUserId) {
            foundPlayer = this.showScoreForCurrentHexUser(score);
        }
        else {
            // 2. 如果是其他用户，查找对应的玩家头像节点
            foundPlayer = this.showScoreForOtherHexUser(userId, score);
        }
        if (!foundPlayer) {
            console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u516D\u8FB9\u5F62\u5934\u50CF\u8282\u70B9\u6765\u663E\u793A\u5206\u6570\u6548\u679C");
        }
    };
    /**
     * 获取当前用户ID（复制四边形棋盘控制器的方法）
     */
    HexChessBoardController.prototype.getCurrentHexUserId = function () {
        var _a, _b;
        return ((_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId) || "";
    };
    /**
     * 为当前用户显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.showScoreForCurrentHexUser = function (score) {
        var _this = this;
        var foundPlayer = false;
        this.hexGridData.forEach(function (gridData) {
            // 如果已经找到了，就不再继续查找
            if (foundPlayer) {
                return;
            }
            if (gridData.hasPlayer && gridData.playerNode) {
                var playerController = gridData.playerNode.getComponent("PlayerGameController") ||
                    gridData.playerNode.getComponent("PlayerGameController ");
                if (playerController) {
                    _this.showScoreOnHexPlayerController(playerController, score);
                    foundPlayer = true;
                }
            }
        });
        if (!foundPlayer) {
            console.warn("❌ 未找到当前用户的六边形头像节点");
        }
        return foundPlayer;
    };
    /**
     * 为其他用户显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.showScoreForOtherHexUser = function (userId, score) {
        if (!this.boardNode) {
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点
        return this.findHexPlayerNodeByUserId(userId, score);
    };
    /**
     * 根据userId查找对应的玩家节点（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.findHexPlayerNodeByUserId = function (userId, score) {
        if (!this.boardNode) {
            console.warn("\u68CB\u76D8\u8282\u70B9\u4E0D\u5B58\u5728\uFF0C\u65E0\u6CD5\u67E5\u627E\u7528\u6237 " + userId + " \u7684\u5934\u50CF");
            return false;
        }
        // 遍历棋盘上的所有玩家头像节点，根据存储的userId精确匹配
        for (var i = 0; i < this.boardNode.children.length; i++) {
            var child = this.boardNode.children[i];
            // 尝试多种方式获取PlayerGameController组件
            var playerController = child.getComponent("PlayerGameController");
            if (!playerController) {
                playerController = child.getComponent("PlayerGameController "); // 注意末尾有空格
            }
            if (!playerController) {
                // 尝试通过类名获取
                var components = child.getComponents(cc.Component);
                playerController = components.find(function (comp) {
                    return comp.constructor.name === 'PlayerGameController' ||
                        comp.constructor.name === 'PlayerGameController ';
                });
            }
            var storedUserId = child['userId'];
            if (storedUserId === userId) {
                if (playerController) {
                    // 找到匹配的用户ID和组件，显示分数效果
                    this.showScoreOnHexPlayerController(playerController, score);
                    return true;
                }
                else {
                    // 找到匹配的用户ID但没有组件
                    console.warn("\u26A0\uFE0F \u627E\u5230\u7528\u6237 " + userId + " \u7684\u8282\u70B9\u4F46\u6CA1\u6709PlayerGameController\u7EC4\u4EF6");
                    return false; // 找到节点但没有组件，返回false
                }
            }
        }
        console.warn("\u274C \u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u516D\u8FB9\u5F62\u5934\u50CF\u8282\u70B9");
        return false;
    };
    /**
     * 在PlayerController上显示分数效果（复制四边形棋盘控制器的逻辑）
     */
    HexChessBoardController.prototype.showScoreOnHexPlayerController = function (playerController, score) {
        // 临时提升节点层级，避免被其他头像遮挡
        var playerNode = playerController.node;
        var originalSiblingIndex = playerNode.getSiblingIndex();
        // 将节点移到最上层
        playerNode.setSiblingIndex(-1);
        // 同时确保加分/减分节点的层级更高
        this.ensureHexScoreNodeTopLevel(playerController);
        if (score > 0) {
            playerController.showAddScore(score);
        }
        else if (score < 0) {
            playerController.showSubScore(Math.abs(score));
        }
        // 延迟恢复原始层级（等分数动画播放完成）
        this.scheduleOnce(function () {
            if (playerNode && playerNode.isValid) {
                playerNode.setSiblingIndex(originalSiblingIndex);
            }
        }, 2.5); // 增加到2.5秒，确保动画完全结束
    };
    /**
     * 确保六边形加分/减分节点在最高层级
     */
    HexChessBoardController.prototype.ensureHexScoreNodeTopLevel = function (playerController) {
        // 设置加分节点的最高层级
        if (playerController.addScoreNode) {
            playerController.addScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
        }
        // 设置减分节点的最高层级
        if (playerController.subScoreNode) {
            playerController.subScoreNode.zIndex = cc.macro.MAX_ZINDEX - 1;
        }
    };
    /**
     * 查找指定用户ID的所有六边形头像节点
     * @param userId 用户ID
     * @returns 头像节点数组
     */
    HexChessBoardController.prototype.findAllHexPlayerNodesByUserId = function (userId) {
        var playerNodes = [];
        if (!this.boardNode) {
            return playerNodes;
        }
        // 遍历棋盘上的所有子节点
        var children = this.boardNode.children;
        for (var i = 0; i < children.length; i++) {
            var child = children[i];
            // 检查是否是玩家预制体（通过组件判断）
            var playerController = child.getComponent(PlayerGameController_1.default);
            if (playerController) {
                // 检查是否是指定用户的头像（使用存储在节点上的userId）
                var storedUserId = child['userId'];
                if (storedUserId === userId) {
                    playerNodes.push(child);
                }
            }
        }
        // 也检查存储在hexGridData中的玩家节点
        this.hexGridData.forEach(function (gridData) {
            if (gridData.hasPlayer && gridData.playerNode) {
                var playerController = gridData.playerNode.getComponent(PlayerGameController_1.default);
                var storedUserId = gridData.playerNode['userId'];
                if (playerController && storedUserId === userId) {
                    // 避免重复添加
                    if (!playerNodes.includes(gridData.playerNode)) {
                        playerNodes.push(gridData.playerNode);
                    }
                }
            }
        });
        return playerNodes;
    };
    // ==================== 其他玩家头像生成 ====================
    // 以下方法与第一张地图的逻辑完全一样，用于生成其他玩家的头像
    /**
     * 在指定六边形位置显示其他玩家的操作（完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 该位置的其他玩家操作列表
     */
    HexChessBoardController.prototype.displayOtherPlayersAtHexPosition = function (q, r, actions) {
        if (!this.isValidHexCoordinate(q, r) || !actions || actions.length === 0) {
            console.warn("\u65E0\u6548\u53C2\u6570: (" + q + ", " + r + "), actions: " + ((actions === null || actions === void 0 ? void 0 : actions.length) || 0));
            return;
        }
        // 检查该位置是否已经有自己的头像
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        if (gridData && gridData.hasPlayer) {
            // 只有当真的有其他玩家时，才调整自己的头像位置
            if (actions.length > 0) {
                // 如果已有自己的头像且有其他玩家，需要使用多人布局策略
                this.addOtherPlayersToExistingHexGrid(q, r, actions);
            }
        }
        else {
            // 如果没有自己的头像，直接添加其他玩家头像
            this.addOtherPlayersToEmptyHexGrid(q, r, actions);
        }
    };
    /**
     * 在已有自己头像的六边形格子上添加其他玩家头像，并调整自己的头像位置和缩放
     * （完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 其他玩家操作列表
     */
    HexChessBoardController.prototype.addOtherPlayersToExistingHexGrid = function (q, r, actions) {
        // 总玩家数 = 自己(1) + 其他玩家数量
        var totalPlayers = 1 + actions.length;
        var positions = this.getHexPlayerPositions(totalPlayers);
        // 第一步：调整自己的头像位置和缩放
        var myPosition = positions[0]; // 第一个位置是自己的
        this.adjustMyHexAvatarPosition(q, r, myPosition, actions);
        // 第二步：从第二个位置开始放置其他玩家
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i + 1]; // 跳过第一个位置（自己的位置）
            // 使用六边形坐标系创建其他玩家头像
            this.createOtherPlayerAtHexPosition(q, r, action, position, totalPlayers);
        }
    };
    /**
     * 在空六边形格子上添加其他玩家头像
     * （完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param actions 其他玩家操作列表
     */
    HexChessBoardController.prototype.addOtherPlayersToEmptyHexGrid = function (q, r, actions) {
        var totalPlayers = actions.length; // 空格子上只有其他玩家
        var positions = this.getHexPlayerPositions(totalPlayers);
        for (var i = 0; i < actions.length; i++) {
            var action = actions[i];
            var position = positions[i];
            // 使用六边形坐标系创建其他玩家头像
            this.createOtherPlayerAtHexPosition(q, r, action, position, totalPlayers);
        }
    };
    /**
     * 根据玩家数量获取六边形布局位置（完全复制四边形棋盘控制器的逻辑）
     * @param playerCount 玩家数量
     * @returns 位置数组 {x: number, y: number, scale: number}[]
     */
    HexChessBoardController.prototype.getHexPlayerPositions = function (playerCount) {
        switch (playerCount) {
            case 1:
                // 单个玩家，居中显示，正常大小
                return [{ x: 0, y: 0, scale: 1.0 }];
            case 2:
                // 两个玩家，左右分布，缩放0.5
                return [
                    { x: -22, y: -8, scale: 0.5 },
                    { x: 22, y: -8, scale: 0.5 } // 右
                ];
            case 3:
                // 三个玩家，上中下分布，缩放0.5
                return [
                    { x: 0, y: 12, scale: 0.5 },
                    { x: -23, y: -27, scale: 0.5 },
                    { x: 23, y: -27, scale: 0.5 } // 右下
                ];
            case 4:
                // 四个玩家，四角分布，缩放0.5
                return [
                    { x: -22, y: 12, scale: 0.5 },
                    { x: 22, y: 12, scale: 0.5 },
                    { x: -22, y: -30, scale: 0.5 },
                    { x: 22, y: -30, scale: 0.5 } // 右下
                ];
            default:
                // 超过4个玩家，只显示前4个
                console.warn("\u73A9\u5BB6\u6570\u91CF\u8FC7\u591A: " + playerCount + "\uFF0C\u53EA\u663E\u793A\u524D4\u4E2A");
                return this.getHexPlayerPositions(4);
        }
    };
    /**
     * 调整自己的六边形头像位置和缩放（当多人在同一格子时）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param position 新的位置和缩放信息
     * @param actions 其他玩家操作列表
     */
    HexChessBoardController.prototype.adjustMyHexAvatarPosition = function (q, r, position, actions) {
        var key = this.getHexKey(q, r);
        var gridData = this.hexGridData.get(key);
        // 查找自己的头像节点
        if (!gridData || !gridData.hasPlayer || !gridData.playerNode) {
            console.warn("\u5728\u516D\u8FB9\u5F62\u4F4D\u7F6E(" + q + ", " + r + ")\u627E\u4E0D\u5230\u81EA\u5DF1\u7684\u5934\u50CF\u8282\u70B9");
            return;
        }
        var myPlayerNode = gridData.playerNode;
        // 计算该格子的总人数（自己 + 其他玩家）
        var totalPlayers = 1 + (actions ? actions.length : 0);
        // 计算基础位置（根据总人数决定是否偏移）
        var basePosition = this.calculateHexBasePositionByPlayerCount(q, r, totalPlayers);
        // 计算新的最终位置
        var newPosition = cc.v2(basePosition.x + position.x, basePosition.y + position.y);
        // 播放平滑移动和缩放动画
        this.playHexAvatarAdjustAnimation(myPlayerNode, newPosition, position.scale);
    };
    /**
     * 根据六边形格子总人数计算基础位置（完全复制四边形棋盘控制器的逻辑）
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param totalPlayers 该格子的总人数
     * @returns 基础位置
     */
    HexChessBoardController.prototype.calculateHexBasePositionByPlayerCount = function (q, r, totalPlayers) {
        if (totalPlayers === 1) {
            // 一个格子里只有一个人：使用正常的偏移（单人头像预制体，y轴+20）
            return this.getHexWorldPosition(q, r, true);
        }
        else {
            // 一个格子里有两个及以上：不偏移（多人头像预制体，不偏移）
            return this.getHexWorldPosition(q, r, false);
        }
    };
    /**
     * 播放六边形头像调整动画（完全复制四边形棋盘控制器的逻辑）
     * @param playerNode 玩家节点
     * @param newPosition 新位置
     * @param newScale 新缩放
     */
    HexChessBoardController.prototype.playHexAvatarAdjustAnimation = function (playerNode, newPosition, newScale) {
        if (!playerNode || !playerNode.isValid) {
            return;
        }
        // 停止之前的动画
        playerNode.stopAllActions();
        // 使用cc.tween播放位置和缩放动画
        cc.tween(playerNode)
            .to(0.3, {
            x: newPosition.x,
            y: newPosition.y,
            scaleX: newScale,
            scaleY: newScale
        }, { easing: 'sineOut' })
            .start();
    };
    /**
     * 创建其他玩家在六边形位置的头像
     * @param q 六边形q坐标
     * @param r 六边形r坐标
     * @param action 玩家操作数据
     * @param position 位置和缩放信息
     * @param totalPlayers 总玩家数
     */
    HexChessBoardController.prototype.createOtherPlayerAtHexPosition = function (q, r, action, position, totalPlayers) {
        var _this = this;
        if (!this.playerGamePrefab || !this.boardNode) {
            console.error("❌ 预制体或棋盘节点未设置！");
            return;
        }
        // 实例化玩家预制体
        var playerNode = cc.instantiate(this.playerGamePrefab);
        // 计算基础位置（根据总人数决定是否偏移）
        var basePosition = this.calculateHexBasePositionByPlayerCount(q, r, totalPlayers);
        // 计算最终位置
        var finalPosition = cc.v2(basePosition.x + position.x, basePosition.y + position.y);
        playerNode.setPosition(finalPosition);
        // 根据总人数设置缩放：单人0.8，多人使用position.scale
        if (totalPlayers === 1) {
            playerNode.setScale(0.8);
        }
        else {
            playerNode.setScale(position.scale);
        }
        // 先隐藏节点，等头像加载完成后再显示
        playerNode.active = false;
        // 添加到棋盘
        this.addPlayerNodeSafely(playerNode);
        // 设置其他玩家的头像和数据
        this.setupOtherPlayerHexAvatar(playerNode, action, function () {
            // 头像加载完成的回调，播放生成动画
            _this.playAvatarSpawnAnimation(playerNode);
        });
    };
    /**
     * 设置其他玩家的六边形头像和数据
     * @param playerNode 玩家节点
     * @param action 玩家操作数据
     * @param onComplete 完成回调
     */
    HexChessBoardController.prototype.setupOtherPlayerHexAvatar = function (playerNode, action, onComplete) {
        // 查找PlayerGameController组件
        var playerController = playerNode.getComponent(PlayerGameController_1.default);
        if (playerController) {
            // 在节点上存储userId，用于后续查找
            playerNode['userId'] = action.userId;
            // 设置旗子节点的显示状态
            var withFlag_1 = (action.action === 2); // action=2表示标记操作，显示旗子
            if (playerController.flagNode) {
                playerController.flagNode.active = withFlag_1;
            }
            // 获取真实的用户数据（和第一张地图逻辑一样）
            var realUserData = this.getRealUserData(action.userId);
            if (!realUserData) {
                console.warn("\u627E\u4E0D\u5230\u7528\u6237 " + action.userId + " \u7684\u771F\u5B9E\u6570\u636E\uFF0C\u4F7F\u7528\u9ED8\u8BA4\u6570\u636E");
                // 使用默认数据作为备选
                realUserData = {
                    userId: action.userId,
                    nickName: "\u73A9\u5BB6" + action.userId,
                    avatar: this.getDefaultAvatarUrl(),
                    score: 0,
                    pos: 0,
                    coin: 0,
                    status: 0,
                    rank: 0
                };
            }
            // 使用PlayerGameController的setData方法来设置头像
            try {
                playerController.setData(realUserData);
                // 延迟设置旗子状态，确保在PlayerGameController初始化之后
                this.scheduleOnce(function () {
                    if (playerController.flagNode) {
                        playerController.flagNode.active = withFlag_1;
                    }
                    onComplete();
                }, 0.1);
            }
            catch (error) {
                console.error("设置其他玩家头像数据失败:", error);
                onComplete();
            }
        }
        else {
            console.warn("⚠️ 找不到PlayerGameController组件");
            onComplete();
        }
    };
    /**
     * 获取其他玩家的头像URL
     * @param userId 用户ID
     * @returns 头像URL
     */
    HexChessBoardController.prototype.getOtherPlayerAvatarUrl = function (userId) {
        // 这里可以根据userId获取真实的头像URL
        // 暂时使用默认头像
        return this.getDefaultAvatarUrl();
    };
    /**
     * 从GlobalBean中获取真实的用户数据（和第一张地图逻辑完全一样）
     * @param userId 用户ID
     * @returns RoomUser 或 null
     */
    HexChessBoardController.prototype.getRealUserData = function (userId) {
        try {
            if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
                console.warn("没有游戏数据，无法获取用户信息");
                return null;
            }
            var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
            var user = users.find(function (u) { return u.userId === userId; });
            if (user) {
                return user;
            }
            else {
                console.warn("\u672A\u627E\u5230\u7528\u6237 " + userId + " \u7684\u6570\u636E");
                return null;
            }
        }
        catch (error) {
            console.error("\u83B7\u53D6\u7528\u6237\u6570\u636E\u65F6\u51FA\u9519: " + error);
            return null;
        }
    };
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "playerGamePrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boomPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "biaojiPrefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom1Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom2Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom3Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom4Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom5Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom6Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom7Prefab", void 0);
    __decorate([
        property(cc.Prefab)
    ], HexChessBoardController.prototype, "boom8Prefab", void 0);
    __decorate([
        property(cc.Node)
    ], HexChessBoardController.prototype, "boardNode", void 0);
    HexChessBoardController = __decorate([
        ccclass
    ], HexChessBoardController);
    return HexChessBoardController;
}(cc.Component));
exports.default = HexChessBoardController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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