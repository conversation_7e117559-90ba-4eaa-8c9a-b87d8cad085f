
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/game/GameScoreController.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'ef3738C6EtNP63rWdv6bSAj', 'GameScoreController');
// scripts/game/GameScoreController.ts

"use strict";
// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GlobalBean_1 = require("../bean/GlobalBean");
var PlayerScoreController_1 = require("../pfb/PlayerScoreController");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var GameScoreController = /** @class */ (function (_super) {
    __extends(GameScoreController, _super);
    function GameScoreController() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.scoreLayout = null; // 分数布局容器
        _this.playerScorePfb = null; // player_score_pfb 预制体
        _this._scoreControllers = []; // 分数控制器数组
        return _this;
        /**
         * 更新player_game_pfb中的change_score显示
         * @param userId 用户ID
         * @param bonusScore 奖励分数
         */
        // update (dt) {}
    }
    // onLoad () {}
    GameScoreController.prototype.onLoad = function () {
        // 初始化时不自动创建界面，等待游戏数据
    };
    GameScoreController.prototype.start = function () {
        // 不在start中自动创建，等待外部调用
    };
    /**
     * 创建分数显示界面
     * 只使用后端传回来的真实游戏数据
     */
    GameScoreController.prototype.createScoreView = function () {
        // 检查必要的组件是否存在
        if (!this.scoreLayout) {
            console.error("scoreLayout 未设置！请在编辑器中拖拽布局节点到 scoreLayout 属性");
            return;
        }
        if (!this.playerScorePfb) {
            console.error("playerScorePfb 未设置！请在编辑器中拖拽预制体到 playerScorePfb 属性");
            return;
        }
        // 只使用后端传回来的真实游戏数据
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法创建分数界面。请等待 NoticeStartGame 消息");
            return;
        }
        // 获取后端传回来的用户数据
        var users = GlobalBean_1.GlobalBean.GetInstance().adjustUserData();
        // 确保所有用户都有score字段，初始化为0
        users.forEach(function (user, index) {
            if (user.score === undefined || user.score === null) {
                user.score = 0;
            }
        });
        // 清空现有的分数显示
        this.scoreLayout.removeAllChildren();
        this._scoreControllers = [];
        // 根据后端用户数据生成分数预制体
        for (var i = 0; i < users.length; i++) {
            var item = cc.instantiate(this.playerScorePfb);
            this.scoreLayout.addChild(item);
            var scoreController = item.getComponent(PlayerScoreController_1.default);
            if (scoreController) {
                this._scoreControllers.push(scoreController);
                scoreController.setData(users[i]);
            }
            else {
                console.error("预制体上没有找到 PlayerScoreController 组件");
            }
        }
    };
    /**
     * 初始化分数界面
     * 当收到 NoticeStartGame 消息后调用此方法
     */
    GameScoreController.prototype.initializeScoreView = function () {
        this.createScoreView();
    };
    /**
     * 设置游戏数据
     * 更新所有玩家的分数显示
     */
    GameScoreController.prototype.setGameData = function () {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法设置分数数据");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().adjustUserData();
        // 更新所有玩家的分数显示
        for (var i = 0; i < users.length; i++) {
            if (i < this._scoreControllers.length) {
                this._scoreControllers[i].setData(users[i]);
            }
        }
    };
    /**
     * 更新特定玩家的分数
     * @param userId 玩家ID
     * @param score 新的分数
     */
    GameScoreController.prototype.updatePlayerScore = function (userId, score) {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新玩家分数");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === userId; });
        if (userIndex !== -1 && userIndex < this._scoreControllers.length) {
            this._scoreControllers[userIndex].updateScore(score);
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u73A9\u5BB6\u6216\u63A7\u5236\u5668: userId=" + userId + ", userIndex=" + userIndex);
        }
    };
    /**
     * 更新所有玩家分数
     */
    GameScoreController.prototype.updateAllScores = function () {
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新所有玩家分数");
            return;
        }
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        for (var i = 0; i < users.length && i < this._scoreControllers.length; i++) {
            this._scoreControllers[i].updateScore(users[i].score || 0);
        }
    };
    /**
     * 获取指定索引的PlayerScoreController
     * @param userIndex 用户索引
     * @returns PlayerScoreController 或 null
     */
    GameScoreController.prototype.getPlayerScoreController = function (userIndex) {
        if (userIndex >= 0 && userIndex < this._scoreControllers.length) {
            return this._scoreControllers[userIndex];
        }
        return null;
    };
    /**
     * 处理首选玩家奖励通知
     * @param data NoticeFirstChoiceBonus 消息数据
     */
    GameScoreController.prototype.onNoticeFirstChoiceBonus = function (data) {
        var _a, _b;
        // 检查是否有游戏数据
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法处理首选玩家奖励");
            return;
        }
        // 查找对应的玩家索引
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === data.userId; });
        if (userIndex !== -1 && userIndex < this._scoreControllers.length) {
            // 1. 更新玩家的总分数到全局数据
            users[userIndex].score = data.totalScore;
            // 2. 更新分数显示 - 显示新的总分
            this._scoreControllers[userIndex].updateScore(data.totalScore);
            // 3. 显示加分效果动画 - 显示奖励分数
            this.showAddScoreWithAnimation(userIndex, data.bonusScore);
            // 4. 判断是否为当前用户，如果是则同时更新player_game_pfb
            var currentUserId = (_b = (_a = GlobalBean_1.GlobalBean.GetInstance().loginData) === null || _a === void 0 ? void 0 : _a.userInfo) === null || _b === void 0 ? void 0 : _b.userId;
            var isMyself = (data.userId === currentUserId);
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u5BF9\u5E94\u7684\u73A9\u5BB6\u63A7\u5236\u5668: userId=" + data.userId + ", userIndex=" + userIndex + ", controllers\u957F\u5EA6=" + this._scoreControllers.length);
            // 打印所有用户信息用于调试
            users.forEach(function (user, index) {
            });
        }
    };
    /**
     * 显示加分效果动画
     * @param userIndex 用户索引
     * @param bonusScore 奖励分数
     */
    GameScoreController.prototype.showAddScoreWithAnimation = function (userIndex, bonusScore) {
        if (userIndex >= 0 && userIndex < this._scoreControllers.length) {
            var scoreController = this._scoreControllers[userIndex];
            // 调用PlayerScoreController的showAddScore方法显示加分效果
            // 这会在player_score_pfb的addscore/change_score中显示"+1"等文本
            scoreController.showAddScore(bonusScore);
        }
        else {
            console.warn("\u65E0\u6548\u7684\u7528\u6237\u7D22\u5F15: " + userIndex + ", \u63A7\u5236\u5668\u6570\u91CF: " + this._scoreControllers.length);
        }
    };
    /**
     * 处理AI托管状态变更
     * @param userId 用户ID
     * @param isAIManaged 是否进入AI托管状态
     */
    GameScoreController.prototype.onAIStatusChange = function (userId, isAIManaged) {
        // 检查是否有游戏数据
        if (!GlobalBean_1.GlobalBean.GetInstance().noticeStartGame || !GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法处理AI托管状态变更");
            return;
        }
        // 查找对应的玩家索引
        var users = GlobalBean_1.GlobalBean.GetInstance().noticeStartGame.users;
        var userIndex = users.findIndex(function (user) { return user.userId === userId; });
        if (userIndex !== -1 && userIndex < this._scoreControllers.length) {
            // 设置对应玩家的AI托管状态显示
            this._scoreControllers[userIndex].setAIManagedStatus(isAIManaged);
            console.log("\u73A9\u5BB6 " + userId + " AI\u6258\u7BA1\u72B6\u6001\u53D8\u66F4: " + (isAIManaged ? '进入托管' : '退出托管'));
        }
        else {
            console.warn("\u627E\u4E0D\u5230\u5BF9\u5E94\u7684\u73A9\u5BB6\u63A7\u5236\u5668: userId=" + userId + ", userIndex=" + userIndex + ", controllers\u957F\u5EA6=" + this._scoreControllers.length);
        }
    };
    __decorate([
        property(cc.Node)
    ], GameScoreController.prototype, "scoreLayout", void 0);
    __decorate([
        property(cc.Prefab)
    ], GameScoreController.prototype, "playerScorePfb", void 0);
    GameScoreController = __decorate([
        ccclass
    ], GameScoreController);
    return GameScoreController;
}(cc.Component));
exports.default = GameScoreController;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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