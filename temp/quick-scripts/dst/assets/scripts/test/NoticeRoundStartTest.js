
                (function() {
                    var nodeEnv = typeof require !== 'undefined' && typeof process !== 'undefined';
                    var __module = nodeEnv ? module : {exports:{}};
                    var __filename = 'preview-scripts/assets/scripts/test/NoticeRoundStartTest.js';
                    var __require = nodeEnv ? function (request) {
                        return cc.require(request);
                    } : function (request) {
                        return __quick_compile_project__.require(request, __filename);
                    };
                    function __define (exports, require, module) {
                        if (!nodeEnv) {__quick_compile_project__.registerModule(__filename, module);}"use strict";
cc._RF.push(module, 'a1b2cPU5fZ4kKvN7xI0VniQ', 'NoticeRoundStartTest');
// scripts/test/NoticeRoundStartTest.ts

"use strict";
// 测试NoticeRoundStart消息处理的脚本
// 这个脚本可以用来模拟发送NoticeRoundStart消息，测试前端计时器更新功能
var __extends = (this && this.__extends) || (function () {
    var extendStatics = function (d, b) {
        extendStatics = Object.setPrototypeOf ||
            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||
            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };
        return extendStatics(d, b);
    };
    return function (d, b) {
        extendStatics(d, b);
        function __() { this.constructor = d; }
        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());
    };
})();
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
Object.defineProperty(exports, "__esModule", { value: true });
var GameMgr_1 = require("../common/GameMgr");
var EventCenter_1 = require("../common/EventCenter");
var MessageId_1 = require("../net/MessageId");
var _a = cc._decorator, ccclass = _a.ccclass, property = _a.property;
var NoticeRoundStartTest = /** @class */ (function (_super) {
    __extends(NoticeRoundStartTest, _super);
    function NoticeRoundStartTest() {
        var _this = _super !== null && _super.apply(this, arguments) || this;
        _this.testButton = null;
        _this.firstChoiceTestButton = null;
        _this.testGameStartAnimationBtn = null;
        _this.testRoundStartAnimationBtn = null;
        _this.testAIStatusChangeBtn = null;
        _this.statusLabel = null;
        return _this;
    }
    NoticeRoundStartTest.prototype.start = function () {
        if (this.testButton) {
            this.testButton.node.on('click', this.sendTestMessage, this);
        }
        if (this.firstChoiceTestButton) {
            this.firstChoiceTestButton.node.on('click', this.testFirstChoiceBonusFlow, this);
        }
        if (this.testGameStartAnimationBtn) {
            this.testGameStartAnimationBtn.node.on('click', this.testGameStartAnimation, this);
        }
        if (this.testRoundStartAnimationBtn) {
            this.testRoundStartAnimationBtn.node.on('click', this.testRoundStartAnimation, this);
        }
        if (this.statusLabel) {
            this.statusLabel.string = '点击按钮测试消息';
        }
    };
    // 发送测试的NoticeRoundStart消息
    NoticeRoundStartTest.prototype.sendTestMessage = function () {
        // 创建测试数据
        var testData = {
            roundNumber: 1,
            countDown: 25,
            gameStatus: 0
        };
        // 模拟接收到的消息格式
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeRoundStart,
            code: 0,
            msg: "success",
            data: testData
        };
        // 发送消息事件
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001\u6D4B\u8BD5\u6D88\u606F: \u56DE\u5408" + testData.roundNumber + ", \u5012\u8BA1\u65F6" + testData.countDown + "\u79D2";
        }
    };
    // 发送倒计时更新测试
    NoticeRoundStartTest.prototype.sendCountdownUpdate = function (seconds) {
        var testData = {
            roundNumber: 1,
            countDown: seconds,
            gameStatus: 0
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeRoundStart,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5012\u8BA1\u65F6\u66F4\u65B0: " + seconds + "\u79D2";
        }
    };
    // 测试不同的倒计时值
    NoticeRoundStartTest.prototype.testDifferentCountdowns = function () {
        var _this = this;
        // 测试25秒倒计时
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(25);
        }, 1);
        // 测试20秒倒计时（进入展示阶段）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(20);
        }, 3);
        // 测试5秒倒计时（回合结束前）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(5);
        }, 5);
        // 测试0秒倒计时（回合结束）
        this.scheduleOnce(function () {
            _this.sendCountdownUpdate(0);
        }, 7);
    };
    // 发送NoticeActionDisplay测试消息
    NoticeRoundStartTest.prototype.sendActionDisplayMessage = function () {
        var testData = {
            roundNumber: 1,
            gameStatus: 0,
            countDown: 5,
            playerActions: [
                {
                    userId: "player_001",
                    x: 3,
                    y: 2,
                    action: 1,
                    score: 1,
                    isFirstChoice: true,
                    result: 2 // 数字2
                },
                {
                    userId: "player_002",
                    x: 1,
                    y: 4,
                    action: 2,
                    score: 1,
                    isFirstChoice: false,
                    result: "correct_mark"
                }
            ],
            playerTotalScores: {
                "player_001": 5,
                "player_002": 3
            },
            remainingMines: 10,
            message: "展示阶段：显示所有玩家操作"
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeActionDisplay,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001ActionDisplay\u6D88\u606F: \u5C55\u793A\u9636\u6BB5\uFF0C\u5269\u4F59" + testData.countDown + "\u79D2\uFF0C\u5269\u4F59\u70B8\u5F39" + testData.remainingMines + "\u4E2A";
        }
    };
    // 测试完整的回合流程
    NoticeRoundStartTest.prototype.testFullRoundFlow = function () {
        var _this = this;
        // 1. 发送回合开始
        this.sendTestMessage();
        // 2. 20秒后发送操作展示
        this.scheduleOnce(function () {
            _this.sendActionDisplayMessage();
        }, 2);
        if (this.statusLabel) {
            this.statusLabel.string = '开始测试完整回合流程...';
        }
    };
    // 测试先手奖励和后续加分流程
    NoticeRoundStartTest.prototype.testFirstChoiceBonusFlow = function () {
        var _this = this;
        // 1. 发送回合开始
        this.sendTestMessage();
        // 2. 2秒后发送先手奖励
        this.scheduleOnce(function () {
            _this.sendFirstChoiceBonusMessage();
        }, 2);
        // 3. 4秒后发送操作展示（包含先手玩家）
        this.scheduleOnce(function () {
            _this.sendActionDisplayWithFirstChoiceMessage();
        }, 4);
        if (this.statusLabel) {
            this.statusLabel.string = '测试先手奖励+本回合加分...';
        }
    };
    // 发送NoticeFirstChoiceBonus测试消息
    NoticeRoundStartTest.prototype.sendFirstChoiceBonusMessage = function () {
        var testData = {
            userId: "player_001",
            roundNumber: 1,
            bonusScore: 1,
            totalScore: 6 // 原来5分 + 1分先手奖励
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeFirstChoiceBonus,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001FirstChoiceBonus: player_001\u83B7\u5F97+1\u5148\u624B\u5956\u52B1";
        }
    };
    // 发送包含先手玩家的NoticeActionDisplay测试消息
    NoticeRoundStartTest.prototype.sendActionDisplayWithFirstChoiceMessage = function () {
        var testData = {
            roundNumber: 1,
            gameStatus: 0,
            countDown: 5,
            playerActions: [
                {
                    userId: "player_001",
                    x: 3,
                    y: 2,
                    action: 1,
                    score: 2,
                    isFirstChoice: true,
                    result: 3 // 数字3
                },
                {
                    userId: "player_002",
                    x: 1,
                    y: 4,
                    action: 2,
                    score: 1,
                    isFirstChoice: false,
                    result: "correct_mark"
                }
            ],
            playerTotalScores: {
                "player_001": 8,
                "player_002": 4 // 原来3分 + 本回合1分
            },
            remainingMines: 9,
            message: "展示阶段：先手玩家应该显示两次player_game_pfb分数变化"
        };
        var messageBean = {
            msgId: MessageId_1.MessageId.MsgTypeNoticeActionDisplay,
            code: 0,
            msg: "success",
            data: testData
        };
        GameMgr_1.GameMgr.Event.Send(EventCenter_1.EventType.ReceiveMessage, messageBean);
        if (this.statusLabel) {
            this.statusLabel.string = "\u5DF2\u53D1\u9001ActionDisplay: \u5148\u624B\u73A9\u5BB6\u5E94\u663E\u793A+2\u5206";
        }
    };
    /**
     * 测试游戏开始动画
     */
    NoticeRoundStartTest.prototype.testGameStartAnimation = function () {
        var _this = this;
        if (this.statusLabel) {
            this.statusLabel.string = "测试游戏开始动画...";
        }
        // 获取GamePageController实例
        var gamePageController = window.gamePageController;
        if (gamePageController) {
            // 调用游戏开始动画
            if (gamePageController.showGameStartAnimation) {
                gamePageController.showGameStartAnimation();
                if (this.statusLabel) {
                    this.statusLabel.string = "游戏开始动画已触发";
                }
                // 3秒后隐藏
                this.scheduleOnce(function () {
                    if (gamePageController.hideGameStartAnimation) {
                        gamePageController.hideGameStartAnimation();
                        if (_this.statusLabel) {
                            _this.statusLabel.string = "游戏开始动画已隐藏";
                        }
                    }
                }, 3);
            }
            else {
                if (this.statusLabel) {
                    this.statusLabel.string = "GamePageController中没有找到showGameStartAnimation方法";
                }
            }
        }
        else {
            if (this.statusLabel) {
                this.statusLabel.string = "未找到GamePageController实例";
            }
        }
    };
    /**
     * 测试回合开始动画
     */
    NoticeRoundStartTest.prototype.testRoundStartAnimation = function () {
        if (this.statusLabel) {
            this.statusLabel.string = "测试回合开始动画...";
        }
        // 获取GamePageController实例
        var gamePageController = window.gamePageController;
        if (gamePageController) {
            // 调用回合开始动画
            if (gamePageController.showRoundStartAnimation) {
                gamePageController.showRoundStartAnimation();
                if (this.statusLabel) {
                    this.statusLabel.string = "回合开始动画已触发";
                }
            }
            else {
                if (this.statusLabel) {
                    this.statusLabel.string = "GamePageController中没有找到showRoundStartAnimation方法";
                }
            }
        }
        else {
            if (this.statusLabel) {
                this.statusLabel.string = "未找到GamePageController实例";
            }
        }
    };
    NoticeRoundStartTest.prototype.onDestroy = function () {
        if (this.testButton) {
            this.testButton.node.off('click', this.sendTestMessage, this);
        }
        if (this.firstChoiceTestButton) {
            this.firstChoiceTestButton.node.off('click', this.testFirstChoiceBonusFlow, this);
        }
        if (this.testGameStartAnimationBtn) {
            this.testGameStartAnimationBtn.node.off('click', this.testGameStartAnimation, this);
        }
        if (this.testRoundStartAnimationBtn) {
            this.testRoundStartAnimationBtn.node.off('click', this.testRoundStartAnimation, this);
        }
    };
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testButton", void 0);
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "firstChoiceTestButton", void 0);
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testGameStartAnimationBtn", void 0);
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testRoundStartAnimationBtn", void 0);
    __decorate([
        property(cc.Button)
    ], NoticeRoundStartTest.prototype, "testAIStatusChangeBtn", void 0);
    __decorate([
        property(cc.Label)
    ], NoticeRoundStartTest.prototype, "statusLabel", void 0);
    NoticeRoundStartTest = __decorate([
        ccclass
    ], NoticeRoundStartTest);
    return NoticeRoundStartTest;
}(cc.Component));
exports.default = NoticeRoundStartTest;

cc._RF.pop();
                    }
                    if (nodeEnv) {
                        __define(__module.exports, __require, __module);
                    }
                    else {
                        __quick_compile_project__.registerModuleFunc(__filename, function () {
                            __define(__module.exports, __require, __module);
                        });
                    }
                })();
//# sourceMappingURL=data:application/json;charset=utf-8;base64,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