
(function () {
var scripts = [{"deps":{"./joui18n-script/LocalizedSprite":1,"./joui18n-script/LocalizedLabel":2,"./assets/meshTools/Singleton":8,"./assets/meshTools/BaseSDK":30,"./assets/meshTools/tools/MeshSdkApi":78,"./assets/meshTools/tools/Publish":79,"./assets/meshTools/tools/MeshSdk":3,"./assets/scripts/TipsDialogController":16,"./assets/scripts/ToastController":54,"./assets/scripts/GlobalManagerController":18,"./assets/scripts/bean/GameBean":4,"./assets/scripts/bean/GlobalBean":19,"./assets/scripts/bean/LanguageType":17,"./assets/scripts/bean/EnumBean":23,"./assets/scripts/common/GameData":27,"./assets/scripts/common/GameMgr":9,"./assets/scripts/common/GameTools":20,"./assets/scripts/common/MineConsole":28,"./assets/scripts/common/EventCenter":21,"./assets/scripts/game/CongratsDialogController":22,"./assets/scripts/game/GamePageController":29,"./assets/scripts/game/GameScoreController":25,"./assets/scripts/game/BtnController":24,"./assets/scripts/game/Chess/GridController":34,"./assets/scripts/game/Chess/HexChessBoardController":5,"./assets/scripts/game/Chess/SingleChessBoardController":26,"./assets/scripts/game/Chess/ChessBoardController":45,"./assets/scripts/hall/HallCenterLayController":37,"./assets/scripts/hall/HallCreateRoomController":75,"./assets/scripts/hall/HallJoinRoomController":31,"./assets/scripts/hall/HallPageController":36,"./assets/scripts/hall/HallParentController":33,"./assets/scripts/hall/InfoDialogController":32,"./assets/scripts/hall/KickOutDialogController":35,"./assets/scripts/hall/LeaveDialogController":38,"./assets/scripts/hall/LevelSelectDemo":63,"./assets/scripts/hall/MatchParentController":39,"./assets/scripts/hall/PlayerLayoutController":40,"./assets/scripts/hall/SettingDialogController":43,"./assets/scripts/hall/TopUpDialogController":41,"./assets/scripts/hall/HallAutoController":42,"./assets/scripts/hall/Level/LevelSelectController":52,"./assets/scripts/hall/Level/LevelSelectExample":6,"./assets/scripts/hall/Level/LevelSelectPageController":55,"./assets/scripts/hall/Level/ScrollViewHelper":47,"./assets/scripts/hall/Level/LevelItemController":44,"./assets/scripts/level/LevelPageController":11,"./assets/scripts/net/GameServerUrl":10,"./assets/scripts/net/HttpManager":51,"./assets/scripts/net/HttpUtils":46,"./assets/scripts/net/IHttpMsgBody":48,"./assets/scripts/net/MessageBaseBean":49,"./assets/scripts/net/MessageId":50,"./assets/scripts/net/WebSocketManager":53,"./assets/scripts/net/WebSocketTool":56,"./assets/scripts/net/ErrorCode":58,"./assets/scripts/pfb/InfoItemController":12,"./assets/scripts/pfb/InfoItemOneController":57,"./assets/scripts/pfb/MatchItemController":61,"./assets/scripts/pfb/PlayerGameController ":59,"./assets/scripts/pfb/PlayerScoreController":67,"./assets/scripts/pfb/SeatItemController":64,"./assets/scripts/pfb/CongratsItemController":60,"./assets/scripts/start_up/StartUpPageController":13,"./assets/scripts/start_up/StartUpCenterController":74,"./assets/scripts/test/NoticeRoundStartTest":14,"./assets/scripts/test/AnimationTest":62,"./assets/scripts/util/AudioMgr":71,"./assets/scripts/util/BlockingQueue":15,"./assets/scripts/util/Config":66,"./assets/scripts/util/Dictionary":65,"./assets/scripts/util/LocalStorageManager":76,"./assets/scripts/util/NickNameLabel":68,"./assets/scripts/util/Tools":70,"./assets/scripts/util/AudioManager":72,"./assets/meshTools/MeshTools":69,"./assets/resources/i18n/zh_HK":7,"./assets/resources/i18n/en":77,"./assets/resources/i18n/zh_CN":73},"path":"preview-scripts/__qc_index__.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedSprite.js"},{"deps":{},"path":"preview-scripts/joui18n-script/LocalizedLabel.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/tools/MeshSdk.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/GameBean.js"},{"deps":{"../../bean/GlobalBean":19,"../../pfb/PlayerGameController ":59},"path":"preview-scripts/assets/scripts/game/Chess/HexChessBoardController.js"},{"deps":{"./LevelSelectController":52},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectExample.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_HK.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/Singleton.js"},{"deps":{"../../meshTools/tools/MeshSdkApi":78,"./EventCenter":21,"./GameData":27,"./GameTools":20,"./MineConsole":28},"path":"preview-scripts/assets/scripts/common/GameMgr.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/GameServerUrl.js"},{"deps":{"../hall/LeaveDialogController":38,"../util/Tools":70,"../util/Config":66,"../GlobalManagerController":18,"../game/Chess/SingleChessBoardController":26,"../net/WebSocketManager":53,"../net/MessageId":50,"../common/GameMgr":9,"../common/EventCenter":21},"path":"preview-scripts/assets/scripts/level/LevelPageController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemController.js"},{"deps":{"../common/GameMgr":9,"./StartUpCenterController":74},"path":"preview-scripts/assets/scripts/start_up/StartUpPageController.js"},{"deps":{"../common/EventCenter":21,"../common/GameMgr":9,"../net/MessageId":50},"path":"preview-scripts/assets/scripts/test/NoticeRoundStartTest.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/BlockingQueue.js"},{"deps":{"./util/Config":66,"./util/Tools":70},"path":"preview-scripts/assets/scripts/TipsDialogController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/LanguageType.js"},{"deps":{"./TipsDialogController":16,"./ToastController":54,"../meshTools/MeshTools":69,"../meshTools/tools/Publish":79,"./bean/GlobalBean":19,"./bean/LanguageType":17,"./bean/EnumBean":23,"./common/GameMgr":9,"./common/EventCenter":21,"./game/GamePageController":29,"./hall/TopUpDialogController":41,"./hall/HallPageController":36,"./level/LevelPageController":11,"./net/GameServerUrl":10,"./net/MessageBaseBean":49,"./net/MessageId":50,"./net/WebSocketManager":53,"./net/WebSocketTool":56,"./net/ErrorCode":58,"./start_up/StartUpPageController":13,"./util/Config":66,"./util/AudioMgr":71},"path":"preview-scripts/assets/scripts/GlobalManagerController.js"},{"deps":{"../../meshTools/Singleton":8,"../hall/HallAutoController":42},"path":"preview-scripts/assets/scripts/bean/GlobalBean.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/common/GameTools.js"},{"deps":{"../../meshTools/Singleton":8,"./GameMgr":9},"path":"preview-scripts/assets/scripts/common/EventCenter.js"},{"deps":{"../bean/GlobalBean":19,"../common/EventCenter":21,"../common/GameMgr":9,"../net/MessageBaseBean":49,"../pfb/CongratsItemController":60,"../util/Config":66,"../util/Tools":70},"path":"preview-scripts/assets/scripts/game/CongratsDialogController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/bean/EnumBean.js"},{"deps":{"../util/AudioManager":72,"../util/Config":66,"../util/LocalStorageManager":76},"path":"preview-scripts/assets/scripts/game/BtnController.js"},{"deps":{"../bean/GlobalBean":19,"../pfb/PlayerScoreController":67},"path":"preview-scripts/assets/scripts/game/GameScoreController.js"},{"deps":{"../../net/WebSocketManager":53,"../../net/MessageId":50},"path":"preview-scripts/assets/scripts/game/Chess/SingleChessBoardController.js"},{"deps":{"../../meshTools/MeshTools":69,"../../meshTools/Singleton":8,"../net/GameServerUrl":10},"path":"preview-scripts/assets/scripts/common/GameData.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/common/MineConsole.js"},{"deps":{"./CongratsDialogController":22,"./GameScoreController":25,"../bean/GlobalBean":19,"../hall/LeaveDialogController":38,"../util/Config":66,"../util/Tools":70,"../util/AudioManager":72,"./Chess/HexChessBoardController":5,"./Chess/ChessBoardController":45,"../pfb/PlayerGameController ":59,"../net/MessageId":50,"../net/WebSocketManager":53},"path":"preview-scripts/assets/scripts/game/GamePageController.js"},{"deps":{},"path":"preview-scripts/assets/meshTools/BaseSDK.js"},{"deps":{"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/HallJoinRoomController.js"},{"deps":{"../util/Config":66,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/InfoDialogController.js"},{"deps":{"../../meshTools/tools/Publish":79,"../bean/GlobalBean":19,"../common/GameMgr":9,"../net/MessageId":50,"../net/WebSocketManager":53,"../ToastController":54,"../util/Config":66,"../util/Tools":70,"./HallCenterLayController":37},"path":"preview-scripts/assets/scripts/hall/HallParentController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/game/Chess/GridController.js"},{"deps":{"../net/MessageId":50,"../net/WebSocketManager":53,"../util/Config":66,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/KickOutDialogController.js"},{"deps":{"../bean/GlobalBean":19,"../common/GameMgr":9,"../net/MessageId":50,"../net/WebSocketManager":53,"../net/WebSocketTool":56,"../ToastController":54,"../util/AudioManager":72,"./HallParentController":33,"./InfoDialogController":32,"./KickOutDialogController":35,"./LeaveDialogController":38,"./Level/LevelSelectPageController":55,"./MatchParentController":39,"./SettingDialogController":43},"path":"preview-scripts/assets/scripts/hall/HallPageController.js"},{"deps":{"../bean/GlobalBean":19,"../net/MessageId":50,"../net/WebSocketManager":53,"../ToastController":54,"./HallAutoController":42,"./HallCreateRoomController":75,"./HallJoinRoomController":31},"path":"preview-scripts/assets/scripts/hall/HallCenterLayController.js"},{"deps":{"../common/GameMgr":9,"../net/MessageId":50,"../net/WebSocketManager":53,"../util/Config":66,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/LeaveDialogController.js"},{"deps":{"../../meshTools/tools/Publish":79,"../bean/GlobalBean":19,"../common/EventCenter":21,"../common/GameMgr":9,"../net/MessageBaseBean":49,"../pfb/MatchItemController":61,"../util/Config":66,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/MatchParentController.js"},{"deps":{"../bean/GlobalBean":19,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/PlayerLayoutController.js"},{"deps":{"../common/GameMgr":9,"../util/Config":66,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/TopUpDialogController.js"},{"deps":{"../bean/GlobalBean":19,"../util/Config":66,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/HallAutoController.js"},{"deps":{"../../meshTools/tools/Publish":79,"../util/AudioManager":72,"../util/Config":66,"../util/LocalStorageManager":76,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/SettingDialogController.js"},{"deps":{"./LevelSelectController":52},"path":"preview-scripts/assets/scripts/hall/Level/LevelItemController.js"},{"deps":{"../../bean/GlobalBean":19,"../../pfb/PlayerGameController ":59},"path":"preview-scripts/assets/scripts/game/Chess/ChessBoardController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/HttpUtils.js"},{"deps":{},"path":"preview-scripts/assets/scripts/hall/Level/ScrollViewHelper.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/IHttpMsgBody.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageBaseBean.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/MessageId.js"},{"deps":{"./HttpUtils":46,"./MessageBaseBean":49,"./GameServerUrl":10,"../../meshTools/MeshTools":69,"../common/GameMgr":9,"../common/EventCenter":21},"path":"preview-scripts/assets/scripts/net/HttpManager.js"},{"deps":{"./ScrollViewHelper":47},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectController.js"},{"deps":{"../../meshTools/Singleton":8,"../common/EventCenter":21,"../common/GameMgr":9,"./WebSocketTool":56},"path":"preview-scripts/assets/scripts/net/WebSocketManager.js"},{"deps":{},"path":"preview-scripts/assets/scripts/ToastController.js"},{"deps":{"../../GlobalManagerController":18,"./LevelSelectController":52,"../../net/MessageId":50,"../../net/WebSocketManager":53},"path":"preview-scripts/assets/scripts/hall/Level/LevelSelectPageController.js"},{"deps":{"./MessageBaseBean":49,"./MessageId":50,"../util/Tools":70,"../../meshTools/Singleton":8,"../common/EventCenter":21,"../common/GameMgr":9},"path":"preview-scripts/assets/scripts/net/WebSocketTool.js"},{"deps":{},"path":"preview-scripts/assets/scripts/pfb/InfoItemOneController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/net/ErrorCode.js"},{"deps":{"../util/Tools":70},"path":"preview-scripts/assets/scripts/pfb/PlayerGameController .js"},{"deps":{"../../meshTools/tools/Publish":79,"../util/Config":66,"../util/NickNameLabel":68,"../util/Tools":70},"path":"preview-scripts/assets/scripts/pfb/CongratsItemController.js"},{"deps":{"../util/NickNameLabel":68,"../util/Tools":70},"path":"preview-scripts/assets/scripts/pfb/MatchItemController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/test/AnimationTest.js"},{"deps":{"./Level/LevelSelectController":52},"path":"preview-scripts/assets/scripts/hall/LevelSelectDemo.js"},{"deps":{"../util/NickNameLabel":68,"../util/Tools":70},"path":"preview-scripts/assets/scripts/pfb/SeatItemController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Dictionary.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/Config.js"},{"deps":{"../bean/GlobalBean":19,"../util/Tools":70,"../util/NickNameLabel":68},"path":"preview-scripts/assets/scripts/pfb/PlayerScoreController.js"},{"deps":{},"path":"preview-scripts/assets/scripts/util/NickNameLabel.js"},{"deps":{"./tools/Publish":79},"path":"preview-scripts/assets/meshTools/MeshTools.js"},{"deps":{"./AudioManager":72,"./Config":66},"path":"preview-scripts/assets/scripts/util/Tools.js"},{"deps":{"./Config":66,"./Dictionary":65},"path":"preview-scripts/assets/scripts/util/AudioMgr.js"},{"deps":{"./AudioMgr":71,"./LocalStorageManager":76},"path":"preview-scripts/assets/scripts/util/AudioManager.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/zh_CN.js"},{"deps":{"../common/EventCenter":21,"../common/GameMgr":9,"../net/MessageBaseBean":49,"../util/Config":66},"path":"preview-scripts/assets/scripts/start_up/StartUpCenterController.js"},{"deps":{"../bean/GlobalBean":19,"../pfb/SeatItemController":64,"../util/Config":66,"../util/Tools":70},"path":"preview-scripts/assets/scripts/hall/HallCreateRoomController.js"},{"deps":{"../../meshTools/Singleton":8},"path":"preview-scripts/assets/scripts/util/LocalStorageManager.js"},{"deps":{},"path":"preview-scripts/assets/resources/i18n/en.js"},{"deps":{"../MeshTools":69,"../BaseSDK":30,"../../scripts/net/MessageBaseBean":49,"../../scripts/common/GameMgr":9,"../../scripts/common/EventCenter":21,"MeshSdk":3},"path":"preview-scripts/assets/meshTools/tools/MeshSdkApi.js"},{"deps":{"../Singleton":8},"path":"preview-scripts/assets/meshTools/tools/Publish.js"}];
var entries = ["preview-scripts/__qc_index__.js"];
var bundleScript = 'preview-scripts/__qc_bundle__.js';

/**
 * Notice: This file can not use ES6 (for IE 11)
 */
var modules = {};
var name2path = {};

// Will generated by module.js plugin
// var scripts = ${scripts};
// var entries = ${entries};
// var bundleScript = ${bundleScript};

if (typeof global === 'undefined') {
    window.global = window;
}

var isJSB = typeof jsb !== 'undefined';

function getXMLHttpRequest () {
    return window.XMLHttpRequest ? new window.XMLHttpRequest() : new ActiveXObject('MSXML2.XMLHTTP');
}

function downloadText(url, callback) {
    if (isJSB) {
        var result = jsb.fileUtils.getStringFromFile(url);
        callback(null, result);
        return;
    }

    var xhr = getXMLHttpRequest(),
        errInfo = 'Load text file failed: ' + url;
    xhr.open('GET', url, true);
    if (xhr.overrideMimeType) xhr.overrideMimeType('text\/plain; charset=utf-8');
    xhr.onload = function () {
        if (xhr.readyState === 4) {
            if (xhr.status === 200 || xhr.status === 0) {
                callback(null, xhr.responseText);
            }
            else {
                callback({status:xhr.status, errorMessage:errInfo + ', status: ' + xhr.status});
            }
        }
        else {
            callback({status:xhr.status, errorMessage:errInfo + '(wrong readyState)'});
        }
    };
    xhr.onerror = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(error)'});
    };
    xhr.ontimeout = function(){
        callback({status:xhr.status, errorMessage:errInfo + '(time out)'});
    };
    xhr.send(null);
};

function loadScript (src, cb) {
    if (typeof require !== 'undefined') {
        require(src);
        return cb();
    }

    // var timer = 'load ' + src;
    // console.time(timer);

    var scriptElement = document.createElement('script');

    function done() {
        // console.timeEnd(timer);
        // deallocation immediate whatever
        scriptElement.remove();
    }

    scriptElement.onload = function () {
        done();
        cb();
    };
    scriptElement.onerror = function () {
        done();
        var error = 'Failed to load ' + src;
        console.error(error);
        cb(new Error(error));
    };
    scriptElement.setAttribute('type','text/javascript');
    scriptElement.setAttribute('charset', 'utf-8');
    scriptElement.setAttribute('src', src);

    document.head.appendChild(scriptElement);
}

function loadScripts (srcs, cb) {
    var n = srcs.length;

    srcs.forEach(function (src) {
        loadScript(src, function () {
            n--;
            if (n === 0) {
                cb();
            }
        });
    })
}

function formatPath (path) {
    let destPath = window.__quick_compile_project__.destPath;
    if (destPath) {
        let prefix = 'preview-scripts';
        if (destPath[destPath.length - 1] === '/') {
            prefix += '/';
        }
        path = path.replace(prefix, destPath);
    }
    return path;
}

window.__quick_compile_project__ = {
    destPath: '',

    registerModule: function (path, module) {
        path = formatPath(path);
        modules[path].module = module;
    },

    registerModuleFunc: function (path, func) {
        path = formatPath(path);
        modules[path].func = func;

        var sections = path.split('/');
        var name = sections[sections.length - 1];
        name = name.replace(/\.(?:js|ts|json)$/i, '');
        name2path[name] = path;
    },

    require: function (request, path) {
        var m, requestScript;

        path = formatPath(path);
        if (path) {
            m = modules[path];
            if (!m) {
                console.warn('Can not find module for path : ' + path);
                return null;
            }
        }

        if (m) {
            let depIndex = m.deps[request];
            // dependence script was excluded
            if (depIndex === -1) {
                return null;
            }
            else {
                requestScript = scripts[ m.deps[request] ];
            }
        }
        
        let requestPath = '';
        if (!requestScript) {
            // search from name2path when request is a dynamic module name
            if (/^[\w- .]*$/.test(request)) {
                requestPath = name2path[request];
            }

            if (!requestPath) {
                if (CC_JSB) {
                    return require(request);
                }
                else {
                    console.warn('Can not find deps [' + request + '] for path : ' + path);
                    return null;
                }
            }
        }
        else {
            requestPath = formatPath(requestScript.path);
        }

        let requestModule = modules[requestPath];
        if (!requestModule) {
            console.warn('Can not find request module for path : ' + requestPath);
            return null;
        }

        if (!requestModule.module && requestModule.func) {
            requestModule.func();
        }

        if (!requestModule.module) {
            console.warn('Can not find requestModule.module for path : ' + path);
            return null;
        }

        return requestModule.module.exports;
    },

    run: function () {
        entries.forEach(function (entry) {
            entry = formatPath(entry);
            var module = modules[entry];
            if (!module.module) {
                module.func();
            }
        });
    },

    load: function (cb) {
        var self = this;

        var srcs = scripts.map(function (script) {
            var path = formatPath(script.path);
            modules[path] = script;

            if (script.mtime) {
                path += ("?mtime=" + script.mtime);
            }
            return path;
        });

        console.time && console.time('load __quick_compile_project__');
        // jsb can not analysis sourcemap, so keep separate files.
        if (bundleScript && !isJSB) {
            downloadText(formatPath(bundleScript), function (err, bundleSource) {
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                if (err) {
                    console.error(err);
                    return;
                }

                let evalTime = 'eval __quick_compile_project__ : ' + srcs.length + ' files';
                console.time && console.time(evalTime);
                var sources = bundleSource.split('\n//------QC-SOURCE-SPLIT------\n');
                for (var i = 0; i < sources.length; i++) {
                    if (sources[i]) {
                        window.eval(sources[i]);
                        // not sure why new Function cannot set breakpoints precisely
                        // new Function(sources[i])()
                    }
                }
                self.run();
                console.timeEnd && console.timeEnd(evalTime);
                cb();
            })
        }
        else {
            loadScripts(srcs, function () {
                self.run();
                console.timeEnd && console.timeEnd('load __quick_compile_project__');
                cb();
            });
        }
    }
};

// Polyfill for IE 11
if (!('remove' in Element.prototype)) {
    Element.prototype.remove = function () {
        if (this.parentNode) {
            this.parentNode.removeChild(this);
        }
    };
}
})();
    