// Learn TypeScript:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/typescript.html
// Learn Attribute:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/reference/attributes.html
// Learn life-cycle callbacks:
//  - https://docs.cocos.com/creator/2.4/manual/en/scripting/life-cycle-callbacks.html

import { RoomUser, NoticeFirstChoiceBonus } from "../bean/GameBean";
import { GlobalBean } from "../bean/GlobalBean";
import PlayerScoreController from "../pfb/PlayerScoreController";

const { ccclass, property } = cc._decorator;

@ccclass
export default class GameScoreController extends cc.Component {

    @property(cc.Node)
    scoreLayout: cc.Node = null;  // 分数布局容器
    @property(cc.Prefab)
    playerScorePfb: cc.Prefab = null;  // player_score_pfb 预制体

    private _scoreControllers: PlayerScoreController[] = []; // 分数控制器数组

    // onLoad () {}

    onLoad() {
        // 初始化时不自动创建界面，等待游戏数据
    }

    start() {
        // 不在start中自动创建，等待外部调用
    }

    /**
     * 创建分数显示界面
     * 只使用后端传回来的真实游戏数据
     */
    createScoreView() {
      

        // 检查必要的组件是否存在
        if (!this.scoreLayout) {
            console.error("scoreLayout 未设置！请在编辑器中拖拽布局节点到 scoreLayout 属性");
            return;
        }

        if (!this.playerScorePfb) {
            console.error("playerScorePfb 未设置！请在编辑器中拖拽预制体到 playerScorePfb 属性");
            return;
        }

        // 只使用后端传回来的真实游戏数据
        if (!GlobalBean.GetInstance().noticeStartGame || !GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法创建分数界面。请等待 NoticeStartGame 消息");
            return;
        }

        // 获取后端传回来的用户数据
        let users: RoomUser[] = GlobalBean.GetInstance().adjustUserData();
        

        // 确保所有用户都有score字段，初始化为0
        users.forEach((user, index) => {
            if (user.score === undefined || user.score === null) {
                user.score = 0;
            }
            
        });

        // 清空现有的分数显示
        this.scoreLayout.removeAllChildren();
        this._scoreControllers = [];

        // 根据后端用户数据生成分数预制体
        for (let i = 0; i < users.length; i++) {
            const item = cc.instantiate(this.playerScorePfb);
            this.scoreLayout.addChild(item);

            let scoreController = item.getComponent(PlayerScoreController);
            if (scoreController) {
                this._scoreControllers.push(scoreController);
                scoreController.setData(users[i]);
                
            } else {
                console.error("预制体上没有找到 PlayerScoreController 组件");
            }
        }

       
    }

    /**
     * 初始化分数界面
     * 当收到 NoticeStartGame 消息后调用此方法
     */
    initializeScoreView() {
        
        this.createScoreView();
    }

    /**
     * 设置游戏数据
     * 更新所有玩家的分数显示
     */
    setGameData() {
        if (!GlobalBean.GetInstance().noticeStartGame || !GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法设置分数数据");
            return;
        }

        let users: RoomUser[] = GlobalBean.GetInstance().adjustUserData();
       

        // 更新所有玩家的分数显示
        for (let i = 0; i < users.length; i++) {
            if (i < this._scoreControllers.length) {
                this._scoreControllers[i].setData(users[i]);
               
            }
        }
    }

    /**
     * 更新特定玩家的分数
     * @param userId 玩家ID
     * @param score 新的分数
     */
    updatePlayerScore(userId: string, score: number) {
        if (!GlobalBean.GetInstance().noticeStartGame || !GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新玩家分数");
            return;
        }

        let users: RoomUser[] = GlobalBean.GetInstance().noticeStartGame.users;
        const userIndex = users.findIndex(user => user.userId === userId);

        if (userIndex !== -1 && userIndex < this._scoreControllers.length) {
            this._scoreControllers[userIndex].updateScore(score);
     
        } else {
            console.warn(`找不到玩家或控制器: userId=${userId}, userIndex=${userIndex}`);
        }
    }

    /**
     * 更新所有玩家分数
     */
    updateAllScores() {
        if (!GlobalBean.GetInstance().noticeStartGame || !GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法更新所有玩家分数");
            return;
        }

        let users: RoomUser[] = GlobalBean.GetInstance().noticeStartGame.users;
       

        for (let i = 0; i < users.length && i < this._scoreControllers.length; i++) {
            this._scoreControllers[i].updateScore(users[i].score || 0);
           
        }
    }

    /**
     * 获取指定索引的PlayerScoreController
     * @param userIndex 用户索引
     * @returns PlayerScoreController 或 null
     */
    getPlayerScoreController(userIndex: number): any {
        if (userIndex >= 0 && userIndex < this._scoreControllers.length) {
            return this._scoreControllers[userIndex];
        }
        return null;
    }

    /**
     * 处理首选玩家奖励通知
     * @param data NoticeFirstChoiceBonus 消息数据
     */
    onNoticeFirstChoiceBonus(data: NoticeFirstChoiceBonus) {
       

        // 检查是否有游戏数据
        if (!GlobalBean.GetInstance().noticeStartGame || !GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法处理首选玩家奖励");
            return;
        }

        // 查找对应的玩家索引
        let users: RoomUser[] = GlobalBean.GetInstance().noticeStartGame.users;
        const userIndex = users.findIndex(user => user.userId === data.userId);

        if (userIndex !== -1 && userIndex < this._scoreControllers.length) {
            

            // 1. 更新玩家的总分数到全局数据
            users[userIndex].score = data.totalScore;
           

            // 2. 更新分数显示 - 显示新的总分
            this._scoreControllers[userIndex].updateScore(data.totalScore);
           
            // 3. 显示加分效果动画 - 显示奖励分数
            this.showAddScoreWithAnimation(userIndex, data.bonusScore);
           

            // 4. 判断是否为当前用户，如果是则同时更新player_game_pfb
            let currentUserId = GlobalBean.GetInstance().loginData?.userInfo?.userId;
            let isMyself = (data.userId === currentUserId);

         
        } else {
            console.warn(`找不到对应的玩家控制器: userId=${data.userId}, userIndex=${userIndex}, controllers长度=${this._scoreControllers.length}`);

            // 打印所有用户信息用于调试
           
            users.forEach((user, index) => {
               
            });
        }
    }

    /**
     * 显示加分效果动画
     * @param userIndex 用户索引
     * @param bonusScore 奖励分数
     */
    private showAddScoreWithAnimation(userIndex: number, bonusScore: number) {


        if (userIndex >= 0 && userIndex < this._scoreControllers.length) {
            let scoreController = this._scoreControllers[userIndex];

            // 调用PlayerScoreController的showAddScore方法显示加分效果
            // 这会在player_score_pfb的addscore/change_score中显示"+1"等文本
            scoreController.showAddScore(bonusScore);

        } else {
            console.warn(`无效的用户索引: ${userIndex}, 控制器数量: ${this._scoreControllers.length}`);
        }
    }

    /**
     * 处理AI托管状态变更
     * @param userId 用户ID
     * @param isAIManaged 是否进入AI托管状态
     */
    onAIStatusChange(userId: string, isAIManaged: boolean) {
        // 检查是否有游戏数据
        if (!GlobalBean.GetInstance().noticeStartGame || !GlobalBean.GetInstance().noticeStartGame.users) {
            console.warn("没有游戏数据，无法处理AI托管状态变更");
            return;
        }

        // 查找对应的玩家索引
        let users: RoomUser[] = GlobalBean.GetInstance().noticeStartGame.users;
        const userIndex = users.findIndex(user => user.userId === userId);

        if (userIndex !== -1 && userIndex < this._scoreControllers.length) {
            // 设置对应玩家的AI托管状态显示
            this._scoreControllers[userIndex].setAIManagedStatus(isAIManaged);

            console.log(`玩家 ${userId} AI托管状态变更: ${isAIManaged ? '进入托管' : '退出托管'}`);
        } else {
            console.warn(`找不到对应的玩家控制器: userId=${userId}, userIndex=${userIndex}, controllers长度=${this._scoreControllers.length}`);
        }
    }

    /**
     * 更新player_game_pfb中的change_score显示
     * @param userId 用户ID
     * @param bonusScore 奖励分数
     */

    // update (dt) {}
}
